import { z } from 'zod';

export type ACLI = 'public-read';

export const ParentFolderE = z.enum([
  'POST',
  'AVATAR',
  'SHIP',
  'PORT',
  'CERTIFICATION',
  'DOCUMENTATION',
  'CHAT',
  'FORUM',
]);
export type ParentFolderI = z.infer<typeof ParentFolderE>;

export type FileTypeValueI = {
  mime: string;
  extension: string;
  maxSize: number;
};
// uc
export const FileExtensionE = z.enum(['png', 'jpeg', 'jpg', 'webp', 'pdf', 'm4a', 'mp3', 'mp4', 'xls', 'xlsx']);

export type FileExtensionI = z.infer<typeof FileExtensionE>;

export const FileTypeE: Record<FileExtensionI, FileTypeValueI> = {
  png: {
    mime: 'image/png',
    extension: 'png',
    maxSize: 500 * 1024,
  },
  jpeg: {
    mime: 'image/jpeg',
    extension: 'jpeg',
    maxSize: 500 * 1024,
  },
  jpg: {
    mime: 'image/jpeg',
    extension: 'jpg',
    maxSize: 500 * 1024,
  },
  webp: {
    mime: 'image/webp',
    extension: 'webp',
    maxSize: 500 * 1024,
  },
  pdf: {
    mime: 'application/pdf',
    extension: 'pdf',
    maxSize: 4 * 1024 * 1024,
  },
  mp3: {
    mime: 'audio/mpeg',
    extension: 'mp3',
    maxSize: 10 * 1024 * 1024,
  },
  m4a: {
    mime: 'audio/m4a',
    extension: 'm4a',
    maxSize: 10 * 1024 * 1024,
  },
  mp4: {
    mime: 'video/mp4',
    extension: 'mp4',
    maxSize: 50 * 1024 * 1024,
  },
  xls: {
    mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xls',
    maxSize: 4 * 1024 * 1024,
  },
  xlsx: {
    mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xls',
    maxSize: 4 * 1024 * 1024,
  },
};
// 4 MB in bytes
export const MAX_FILE_SIZE: number = 4 * 1024 * 1024;
export const MAX_NO_OF_FILES = 8;

export const DocumentAllowedMimeTypeE = z.enum([
  FileTypeE.jpeg.mime,
  FileTypeE.jpg.mime,
  FileTypeE.webp.mime,
  FileTypeE.pdf.mime,
]);

export const ImageAllowedMimeTypeE = z.enum([FileTypeE.jpeg.mime, FileTypeE.jpg.mime, FileTypeE.webp.mime]);

export const AllowedMimeTypeE = DocumentAllowedMimeTypeE.or(ImageAllowedMimeTypeE);
