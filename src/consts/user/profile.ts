import { z } from 'zod';
import { GenderE as Gender, ProfileStatusE as ProfileStatus } from '@prisma/postgres';

export const GenderE = z.enum([Gender.MALE, Gender.FEMALE, Gender.OTHER]);
export type GenderI = z.infer<typeof GenderE>;

export const ProfileStatusE = z.enum([
  ProfileStatus.ACTIVE,
  ProfileStatus.INACTIVE,
  ProfileStatus.SCHEDULED_FOR_DELETION,
  ProfileStatus.BLOCKED,
  ProfileStatus.DELETED,
]);
export type ProfileStatusI = z.infer<typeof ProfileStatusE>;
