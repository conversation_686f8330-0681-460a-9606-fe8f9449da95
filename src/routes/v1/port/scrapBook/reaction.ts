import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import {
  ScrapBookReactionCreateOneParamsSchema,
  ScrapBookReactionPostIdSchema,
  ScrapBookReactionFetchForClientParamsSchema,
} from '@schemas/port/reaction';
import { pick } from '@utils/data/object';
import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const scrapBookReactionRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/scrap-book/reactions', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ScrapBookReactionFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SCBKRCN005', queryError);
    }
    const result = await Port.ScrapBookReactionModule.fetchForClient(
      pick(queryData, ['scrapBookPostId']),
      pick(queryData, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post(
    '/backend/api/v1/port/scrap-book/reaction',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: bodyData, error: bodyError } = ScrapBookReactionCreateOneParamsSchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('SCBKRCN007', { error: bodyError.errors });
      }

      const result = await Port.ScrapBookReactionModule.upsertOne(request, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.delete(
    '/backend/api/v1/port/scrap-book/reaction',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = ScrapBookReactionPostIdSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('SCBKRCN007', queryError);
      }

      await Port.ScrapBookReactionModule.deleteOne(request, queryData);
      reply.status(HttpStatus.NO_CONTENT).send();
    },
  );
};

export default scrapBookReactionRoutes;
