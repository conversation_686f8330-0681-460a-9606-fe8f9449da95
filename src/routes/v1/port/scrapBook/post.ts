import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import { RouteParamsSchema } from '@schemas/common/common';
import { ScrapBookPostCreateOneParamsSchema, ScrapBookPostFetchForClientParamsSchema } from '@schemas/port/post';
import type { FastifyInstance, FastifyReply } from 'fastify';
const scrapBookPostRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/scrap-book/posts', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ScrapBookPostFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SCBKPST005', queryError);
    }
    const result = await Port.ScrapBookPostModule.fetchForClient(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/port/scrap-book/post/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('SCBKPST006', routeParamsError);
    }
    const result = await Port.ScrapBookPostModule.fetchOne(request, {
      id: routeParamsData.id,
    });
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/port/scrap-book/post', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ScrapBookPostCreateOneParamsSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('SCBKPST007', { error: bodyError.errors });
    }
    const result = await Port.ScrapBookPostModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.patch('/backend/api/v1/port/scrap-book/post/:id', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('SCBKPST006', routeParamsError);
    }
    const { data: bodyData, error: bodyError } = ScrapBookPostCreateOneParamsSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('SCBKPST007', { error: bodyError.errors });
    }
    const result = await Port.ScrapBookPostModule.updateOne(
      request,
      {
        id: routeParamsData.id,
      },
      bodyData,
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/port/scrap-book/post', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(
      JSON.parse(request.body as string),
    );
    if (routeParamsError) {
      throw new AppError('SCBKPST006', routeParamsError);
    }
    await Port.ScrapBookPostModule.deleteOne(request, {
      id: routeParamsData.id,
    });
    reply.status(HttpStatus.NO_CONTENT);
  });
  fastify.get(
    '/backend/api/v1/port/scrap-book/post/:id/caption',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);

      if (routeParamsError) {
        throw new AppError('POST022', routeParamsError);
      }

      const result = await Port.ScrapBookPostModule.fetchFullTextPreview({ id: routeParamsData.id });
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default scrapBookPostRoutes;
