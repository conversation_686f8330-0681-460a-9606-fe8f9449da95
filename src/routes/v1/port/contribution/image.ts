import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';

import { PortImageFetchForClientParamsSchema } from '@schemas/port/contribution';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply } from 'fastify';

const contributionImageRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/contribution/image', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = PortImageFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRTCNB005', queryError);
    }
    const result = await Port.PortImageContributionModule.fetchForClient(
      queryData,
      pick(queryData, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
};

export default contributionImageRoutes;
