import { HttpStatus } from '@consts/common/api/status';
import {
  EquipmentCategoryFetchForClientSchema,
  EquipmentCategoryFetchsertI,
  EquipmentCategoryFetchsertSchema,
} from '@schemas/ship/equipmentCategory';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import Ship from '@modules/ship';
import AppError from '@classes/AppError';

const equipmentCategoryRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/ship/equipment-category/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error, data: queryData } = EquipmentCategoryFetchForClientSchema.safeParse(request.query);
      if (error) {
        throw new AppError('PORT005', error);
      }
      const result = await Ship.EquipmentCategoryModule.fetchForClient(
        pick(queryData, ['search']),
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/ship/equipment-category/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const body = EquipmentCategoryFetchsertSchema.parse(request.body);
      const result = await Ship.EquipmentCategoryModule.fetchsert(body as EquipmentCategoryFetchsertI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default equipmentCategoryRoutes;
