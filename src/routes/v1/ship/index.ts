import { FastifyInstance } from 'fastify';
import shipChildRoutes from './ship';
import contributionDataRoutes from './contribution/data';
import vesselTypeRoutes from './mainVesselType';
import fuelTypeRoutes from './fuelType';
import equipmentCategoryRoutes from './equipmentCategory';
import subVesselTypeRoutes from './subVesselType';
import visitorRoutes from './visitor';
import equipmentManufacturerRoutes from './equipmentManufacturer';
import equipmentModelRoutes from './equipmentModel';
// import contributionImageRoutes from './contribution/image';

const shipRoutes = (fastify: FastifyInstance): void => {
  fastify.register(shipChildRoutes);
  fastify.register(contributionDataRoutes);
  fastify.register(fuelTypeRoutes);
  fastify.register(vesselTypeRoutes);
  fastify.register(equipmentCategoryRoutes);
  fastify.register(equipmentManufacturerRoutes);
  fastify.register(equipmentModelRoutes);
  // fastify.register(contributionImageRoutes);
  fastify.register(subVesselTypeRoutes);
  fastify.register(visitorRoutes);
};

export default shipRoutes;
