import { HttpStatus } from '@consts/common/api/status';
import {
  EquipmentManufacturerFetchForClientSchema,
  EquipmentManufacturerFetchsertI,
  EquipmentManufacturerFetchsertSchema,
} from '@schemas/ship/equipmentManufacturer';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import Ship from '@modules/ship';
import AppError from '@classes/AppError';

const equipmentManufacturerRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/ship/equipment-manufacturer/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error, data: queryData } = EquipmentManufacturerFetchForClientSchema.safeParse(request.query);
      if (error) {
        throw new AppError('EQMNF001', error);
      }
      const result = await Ship.EquipmentManufacturerModule.fetchForClient(
        pick(queryData, ['search']),
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/ship/equipment-manufacturer/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const body = EquipmentManufacturerFetchsertSchema.parse(request.body);
      const result = await Ship.EquipmentManufacturerModule.fetchsert(body as EquipmentManufacturerFetchsertI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default equipmentManufacturerRoutes;
