import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import User from '@modules/user';
import { RouteParamsSchema } from '@schemas/common/common';
import { BasicProfilesQuerySchema } from '@schemas/user/basics';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { BasicProfilesParamsI } from '@interfaces/user/basics';

const basicRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/user/profile/basic/:id', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data } = RouteParamsSchema.safeParse(request.params);
    if (error) {
      throw new AppError('PFL011', error);
    }
    const result = await User.ProfileModule.fetchBasic({ id: data.id });
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get(
    '/backend/api/v1/user/profile/basic-profiles',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error, data } = BasicProfilesQuerySchema.safeParse(request.query);
      if (error) {
        throw new AppError('PFL011', error);
      }
      const params: BasicProfilesParamsI = {
        profileIds: data.profileIds,
      };
      const result = await User.ProfileModule.fetchBasicProfiles(params);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default basicRoutes;
