import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import User from '@modules/user';
import type { FastifyInstance, FastifyReply } from 'fastify';

const statusRoutes = (fastify: FastifyInstance): void => {
  fastify.patch(
    '/backend/api/v1/user/profile-status/deactivate',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      await User.ProfileModule.deactivate(request);
      reply.status(HttpStatus.OK).send({});
    },
  );
  fastify.delete(
    '/backend/api/v1/user/profile-status/delete',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      await User.ProfileModule.deleteOne(request);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};

export default statusRoutes;
