import { FastifyInstance } from 'fastify';
import onBoardingRoutes from './profile/onBoarding';
import bioRoutes from './profile/bio';
import usernameRoutes from './profile/username';
import basicRoutes from './profile/basic';
import dataRoutes from './profile/data';
import aboutRoutes from './profile/about';
import statusRoutes from './profile/status';

const userRoutes = (fastify: FastifyInstance): void => {
  fastify.register(onBoardingRoutes);
  fastify.register(aboutRoutes);
  fastify.register(basicRoutes);
  fastify.register(dataRoutes);
  fastify.register(bioRoutes);
  fastify.register(usernameRoutes);
  fastify.register(statusRoutes);
};

export default userRoutes;
