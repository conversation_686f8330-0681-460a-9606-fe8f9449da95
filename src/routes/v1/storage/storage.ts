import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import StorageModule from '@modules/storage';
import { PresignedURLsSchema, DeleteFileSchema } from '@schemas/storage/storage';

import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const storageChildRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/storage/presigned-url', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data, error } = PresignedURLsSchema.safeParse(request.body);

    if (error) {
      throw new AppError('STRG003', error);
    }
    const presignedURLsResult = await StorageModule.CoreStorageModule.presignedURLsBulk(data);
    reply.status(HttpStatus.CREATED).send(presignedURLsResult);
  });

  fastify.delete('/backend/api/v1/storage/file', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data, error } = DeleteFileSchema.safeParse(request.query);
    if (error) {
      throw new AppError('STRG003', error);
    }
    await StorageModule.CoreStorageModule.deleteFile(data as { fileUrl: string });
    reply.status(HttpStatus.NO_CONTENT).send();
  });
};

export default storageChildRoutes;
