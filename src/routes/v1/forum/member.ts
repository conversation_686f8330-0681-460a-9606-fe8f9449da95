import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';

import ForumModule from '@modules/forum';
import {
  CommunityMemberFetchForClientSchema,
  // CommunityMemberTypeChangeApprovalBodySchema,
  // CommunityMemberTypeChangeApprovalQuerySchema,
  //  CommunityMemberTypeChangeSchema
} from '@schemas/forum/member';
import { FastifyInstance, FastifyReply } from 'fastify';

const communityMemberRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/community-member', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = CommunityMemberFetchForClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN002', queryError);
    }
    const result = await ForumModule.CommunityMemberModule.fetchMemberForClient(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete(
    '/backend/api/v1/forum/community-member',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = CommunityMemberFetchForClientSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      await ForumModule.CommunityMemberModule.leaveCommunity(queryData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};

export default communityMemberRoutes;
