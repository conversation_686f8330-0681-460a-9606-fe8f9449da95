import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import {
  ForumQuestionVoteCreateOneSchema,
  ForumQuestionVoteDeleteOneSchema,
  ForumQuestionVoteFetchManySchema,
} from '@schemas/forum/vote';
import { FastifyInstance, FastifyReply } from 'fastify';

const questionVoteRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/question-votes', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumQuestionVoteFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('FMAVT006', queryError);
    }
    const result = await ForumModule.QuestionVoteModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/forum/question-vote', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ForumQuestionVoteCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('FMAVT007', bodyError);
    }
    const result = await ForumModule.QuestionVoteModule.createOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/forum/question-vote', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumQuestionVoteDeleteOneSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('FMAVT008', queryError);
    }
    await ForumModule.QuestionVoteModule.deleteOne(request, queryData);
    reply.status(HttpStatus.NO_CONTENT);
  });
};
export default questionVoteRoutes;
