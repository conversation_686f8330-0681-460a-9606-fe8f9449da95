import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { RouteParamsSchema } from '@schemas/common/common';
import {
  ForumAnswerCommentFetchManySchema,
  ForumAnswerCommentCreateOneSchema,
  ForumAnswerCommentFetchRepliesSchema,
} from '@schemas/forum/answerComment';
import { FastifyInstance, FastifyReply } from 'fastify';

const answerCommentRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/answer-comments', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumAnswerCommentFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('FMACMT004', queryError);
    }
    const result = await ForumModule.AnswerCommentModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get(
    '/backend/api/v1/forum/answer-comment-replies',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = ForumAnswerCommentFetchRepliesSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      const result = await ForumModule.AnswerCommentModule.fetchReplies(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post('/backend/api/v1/forum/answer-comment', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ForumAnswerCommentCreateOneSchema.safeParse(request.body);

    if (bodyError) {
      throw new AppError('FMACMT005', bodyError);
    }
    const result = await ForumModule.AnswerCommentModule.createOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete(
    '/backend/api/v1/forum/answer-comment/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
      if (routeParamsError) {
        throw new AppError('FMACMT006', routeParamsError);
      }
      await ForumModule.AnswerCommentModule.deleteOne(request, routeParamsData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};
export default answerCommentRoutes;
