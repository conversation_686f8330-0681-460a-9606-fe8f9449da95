import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { RouteParamsSchema } from '@schemas/common/common';
import {
  CommunityCreateOneSchema,
  // CommunityFetchOneForExternalClientParamsSchema,
  CommunityUpdateOneSchema,
  CommunityFetchForClientSchema,
  //  CommunityFetchForClientI,
} from '@schemas/forum/community';
import { FastifyInstance, FastifyReply } from 'fastify';

const communityRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/forum/community', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = CommunityCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('CMTY001', bodyError);
    }
    const result = await ForumModule.CommunityModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.patch('/backend/api/v1/forum/community', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = RouteParamsSchema.safeParse(request.query);
    const { data: bodyData, error: bodyError } = CommunityUpdateOneSchema.safeParse(request.body);
    if (queryError) {
      throw new AppError('CMTY002', queryError);
    }
    if (bodyError) {
      throw new AppError('CMTY002', bodyError);
    }
    const result = await ForumModule.CommunityModule.updateOne(request, bodyData, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/forum/community', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = RouteParamsSchema.safeParse(request.query);
    if (bodyError) {
      throw new AppError('POST008', bodyError);
    }
    const result = await ForumModule.CommunityModule.deleteOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.get('/backend/api/v1/forum/community', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = CommunityFetchForClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('CMTY006', queryError);
    }

    const result = await ForumModule.CommunityModule.fetchForClient(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  // fastify.get('/backend/api/v1/forum/community/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
  //   const { data: paramsData, error: paramsError } = CommunityFetchOneForExternalClientParamsSchema.safeParse(
  //     request.params,
  //   );
  //   if (paramsError) {
  //     throw new AppError('EXP011', paramsError);
  //   }
  //   const result = await ForumModule.CommunityModule.fetchForClient(paramsData);
  //   reply.status(HttpStatus.OK).send(result);
  // });
};

export default communityRoutes;
