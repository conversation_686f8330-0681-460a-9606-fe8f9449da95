import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';

import ForumModule from '@modules/forum';

import {
  QuestionCommentCreateOneSchema,
  QuestionCommentFetchManySchema,
  QuestionCommentEditOneSchema,
  QuestionCommentDeleteOneSchema,
  QuestionCommentFetchRepliesSchema,
} from '@schemas/forum/questionComment';
import { FastifyInstance, FastifyReply } from 'fastify';

const questionCommentRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/forum/question-comment', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = QuestionCommentCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('GEN002', bodyError);
    }
    const result = await ForumModule.QuestionCommentModule.createOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/forum/question-comments', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = QuestionCommentFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN002', queryError);
    }
    const result = await ForumModule.QuestionCommentModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get(
    '/backend/api/v1/forum/question-comment-replies',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = QuestionCommentFetchRepliesSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      const result = await ForumModule.QuestionCommentModule.fetchReplies(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.patch('/backend/api/v1/forum/question-comment', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = QuestionCommentEditOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('GEN002', bodyError);
    }
    const result = await ForumModule.QuestionCommentModule.editOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.delete(
    '/backend/api/v1/forum/question-comment',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = QuestionCommentDeleteOneSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      const result = await ForumModule.QuestionCommentModule.deleteOne(request, queryData);
      reply.status(HttpStatus.NO_CONTENT).send(result);
    },
  );
};

export default questionCommentRoutes;
