import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { GlobalSearchParamsSchema } from '@schemas/forum/question';
import type { FastifyInstance, FastifyReply } from 'fastify';

const forumGlobalSearchRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/global-search/questions', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = GlobalSearchParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('FMQUE009', queryError);
    }
    const result = await ForumModule.QuestionModule.globalSearch(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/forum/global-search/communities', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = GlobalSearchParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN005', queryError);
    }
    const result = await ForumModule.CommunityModule.globalSearch(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/forum/global-search', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = GlobalSearchParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN005', queryError);
    }
    
    // Search both questions and communities in parallel
    const [questionsResult, communitiesResult] = await Promise.all([
      ForumModule.QuestionModule.globalSearch(request, queryData),
      ForumModule.CommunityModule.globalSearch(request, queryData),
    ]);

    const response = {
      questions: questionsResult,
      communities: communitiesResult,
    };

    reply.status(HttpStatus.OK).send(response);
  });
};

export default forumGlobalSearchRoutes;
