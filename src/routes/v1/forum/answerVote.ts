import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import {
  ForumAnswerVoteCreateOneSchema,
  ForumAnswerVoteDeleteOneSchema,
  ForumAnswerVoteFetchManySchema,
} from '@schemas/forum/vote';
import type { FastifyInstance, FastifyReply } from 'fastify';

const answerVoteRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/answer-votes', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumAnswerVoteFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('FMAVT006', queryError);
    }
    const result = await ForumModule.AnswerVoteModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/forum/answer-vote', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ForumAnswerVoteCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('FMAVT007', bodyError);
    }
    const result = await ForumModule.AnswerVoteModule.createOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/forum/answer-vote', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = ForumAnswerVoteDeleteOneSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FMAVT008', error);
    }
    await ForumModule.AnswerVoteModule.deleteOne(request, data);
    reply.status(HttpStatus.NO_CONTENT);
  });
};
export default answerVoteRoutes;
