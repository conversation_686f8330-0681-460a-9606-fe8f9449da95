import { FastifyInstance } from 'fastify';
import registerRoutes from './register';
import loginRoutes from './login';
import authUsernameRoutes from './onboarding/username';
import forgotPasswordRoutes from './forgot';

const authRoutes = (fastify: FastifyInstance): void => {
  fastify.register(authUsernameRoutes);
  fastify.register(loginRoutes);
  fastify.register(registerRoutes);
  fastify.register(forgotPasswordRoutes);
};

export default authRoutes;
