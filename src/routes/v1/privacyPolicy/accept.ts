import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import PrivacyPolicyModule from '@modules/privacyPolicy/privacyPolicy';
import type { FastifyInstance, FastifyReply } from 'fastify';

const privacyPolicyAcceptRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/privacy-policy/accept', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await PrivacyPolicyModule.acceptPrivacyPolicy(request);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default privacyPolicyAcceptRoutes;
