import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import PrivacyPolicyModule from '@modules/privacyPolicy/privacyPolicy';
import { PrivacyPolicyFetchSchema } from '@schemas/privacyPolicy/privacyPolicy';
import type { FastifyInstance, FastifyReply } from 'fastify';

const privacyPolicyFetchRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/privacy-policy', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = PrivacyPolicyFetchSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN002', queryError);
    }
    const result = await PrivacyPolicyModule.fetch(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default privacyPolicyFetchRoutes;
