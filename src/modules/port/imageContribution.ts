import { prismaPG } from '@config/db';
import { PAGINATION } from '@consts/common/pagination';
import { PaginationI } from '@schemas/common/common';
import { PortModule } from './port';
import { PortImageContribution, Prisma } from '@prisma/postgres';
import { PortImageFetchForClientParamsI } from '@schemas/port/contribution';
import type { PortUploadImagesBulkParamsI } from '@interfaces/port/contribution';

import { FastifyStateI } from '@interfaces/common/declaration';

export const PortImageContributionModule = {
  fetchForClient: async (params: PortImageFetchForClientParamsI, pagination: PaginationI = PAGINATION) => {
    await PortModule.fetchByUnLocode({
      unLocode: params.unLocode,
      dataType: params.dataType,
    });
    const filter: Prisma.PortImageContributionWhereInput = {
      dataStatus: {
        in: ['APPROVED', 'SAVED'],
      },
    };
    if (params.dataType === 'master') {
      filter.portUnLocode = params.unLocode;
    } else if (params.dataType === 'raw') {
      filter.portRawDataUnLocode = params.unLocode;
    }
    const portImageContributionResult = await prismaPG.portImageContribution.findMany({
      select: {
        id: true,
        imageUrl: true,
      },
      where: filter,
      orderBy: {
        createdAt: 'desc',
      },
      skip: pagination.page,
      take: pagination.pageSize,
    });
    return portImageContributionResult;
  },
  uploadImagesBulk: async (
    state: FastifyStateI,
    { fileUrls, unLocode, dataType }: PortUploadImagesBulkParamsI,
  ): Promise<Pick<PortImageContribution, 'id' | 'imageUrl'>[]> => {
    const _portResult = await PortModule.fetchByUnLocode({ dataType, unLocode });
    const createInput = fileUrls.map((imageUrl) => {
      const createInputItem = {
        imageUrl,
        profileId: state.profileId,
      } as Prisma.PortImageContributionUncheckedCreateInput;
      if (dataType === 'master') {
        createInputItem.portUnLocode = unLocode;
      } else if (dataType === 'raw') {
        createInputItem.portRawDataUnLocode = unLocode;
      }
      return createInputItem;
    });
    const portImageContributionResult = await prismaPG.portImageContribution.createManyAndReturn({
      data: createInput,
      select: {
        id: true,
        imageUrl: true,
      },
    });
    return portImageContributionResult;
  },
};
