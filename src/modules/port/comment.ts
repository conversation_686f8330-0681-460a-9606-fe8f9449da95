import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  ScrapBookCommentCreateOneResultI,
  ScrapBookCommentFetchManyResultI,
  ScrapBookCommentFetchForClientI,
  ProfileItemI,
  ScrapBookCommentTempResultItemI,
} from '@interfaces/port/comment';
import { Prisma, PostStatusE } from '@prisma/postgres';
import {
  ScrapBookCommentCreateOneI,
  ScrapBookCommentFetchManyI,
  ScrapBookCommentFetchRepliesI,
} from '@schemas/port/comment';
export const ScrapBookCommentModule = {
  fetchMany: async (
    state: FastifyStateI,
    { scrapBookPostId, cursorId, pageSize }: ScrapBookCommentFetchManyI,
  ): Promise<ScrapBookCommentFetchManyResultI> => {
    const selfProfileId = state.profileId;

    const [commentsCountResult, commentTempResult] = await Promise.all([
      prismaPG.$queryRaw<{ total: bigint }[]>`
      SELECT COUNT(DISTINCT c."id") AS "total"
      FROM "port"."ScrapBookComment" c
      INNER JOIN "user"."Profile" u ON u."id" = c."profileId"
      WHERE c."scrapBookPostId" = ${scrapBookPostId}::uuid
        AND c."parentCommentId" IS NULL
        AND c."status" =  ${PostStatusE.ACTIVE}::"feed"."PostStatusE"
        AND NOT EXISTS (
          SELECT 1
          FROM "network"."BlockedProfile" b
          WHERE (
            (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
            OR
            (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
          )
          LIMIT 1
        )
    `,
      prismaPG.$queryRaw<
        (ScrapBookCommentTempResultItemI & {
          replies: ScrapBookCommentTempResultItemI[];
          repliesCount: bigint;
        })[]
      >`
      SELECT
          c."id",
          c."text",
          c."cursorId",
          c."createdAt",
          c."updatedAt",
          json_build_object(
            'id', u."id",
            'name', u."name",
            'avatar', u."avatar",
            'designationText', u."designationText",
            'designationAlternativeId', u."designationAlternativeId",
            'designationRawDataId', u."designationRawDataId",
            'entityText', u."entityText",
            'entityId', u."entityId",
            'entityRawDataId', u."entityRawDataId"
          ) AS "Profile",
          (
            SELECT COUNT(r."id")::bigint
            FROM "port"."ScrapBookComment" r
            WHERE r."parentCommentId" = c."id"
              AND r."status" =  ${PostStatusE.ACTIVE}::"feed"."PostStatusE"
              AND NOT EXISTS (
                SELECT 1
                FROM "network"."BlockedProfile" b
                JOIN "user"."Profile" up ON up."id" = r."profileId"
                WHERE (
                  (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = up."id")
                  OR
                  (b."blockerId" = up."id" AND b."blockedId" = ${selfProfileId}::uuid)
                )
                LIMIT 1
              )
          ) AS "repliesCount",
          COALESCE(
            (
              SELECT json_agg(
                json_build_object(
                  'id', r."id",
                  'text', r."text",
                  'cursorId', r."cursorId",
                  'createdAt', r."createdAt",
                  'updatedAt', r."updatedAt",
                  'Profile', json_build_object(
                    'id', ru."id",
                    'name', ru."name",
                    'avatar', ru."avatar",
                    'designationText', ru."designationText",
                    'designationAlternativeId', ru."designationAlternativeId",
                    'designationRawDataId', ru."designationRawDataId",
                    'entityText', ru."entityText",
                    'entityId', ru."entityId",
                    'entityRawDataId', ru."entityRawDataId"
                  )
                )
              )
              FROM (
                SELECT
                  r."id",
                  r."text",
                  r."cursorId",
                  r."createdAt",
                  r."updatedAt",
                  r."profileId"
                FROM "port"."ScrapBookComment" r
                WHERE r."parentCommentId" = c."id"
                  AND r."status" =  ${PostStatusE.ACTIVE}::"feed"."PostStatusE"
                  AND NOT EXISTS (
                    SELECT 1
                    FROM "network"."BlockedProfile" b
                    JOIN "user"."Profile" ru ON ru."id" = r."profileId"
                    WHERE (
                      (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = ru."id")
                      OR
                      (b."blockerId" = ru."id" AND b."blockedId" = ${selfProfileId}::uuid)
                    )
                    LIMIT 1
                  )
                ORDER BY r."cursorId" DESC
                LIMIT 2
              ) r
              JOIN "user"."Profile" ru ON ru."id" = r."profileId"
            ),
            '[]'::json
          ) AS "replies"
        FROM "port"."ScrapBookComment" c
        INNER JOIN "user"."Profile" u ON u."id" = c."profileId"
        WHERE c."scrapBookPostId" = ${scrapBookPostId}::uuid
          AND c."parentCommentId" IS NULL
          AND c."status" =  ${PostStatusE.ACTIVE}::"feed"."PostStatusE"
          ${cursorId ? Prisma.sql`AND c."cursorId" <= ${BigInt(cursorId)}` : Prisma.empty}
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE (
              (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
              OR
              (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
            )
            LIMIT 1
          )
        ORDER BY c."cursorId" DESC
        LIMIT ${pageSize}
      `,
    ]);
    const transformProfile = (profile: ProfileItemI) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const transformComment = (
      item: ScrapBookCommentTempResultItemI,
    ): Omit<ScrapBookCommentFetchForClientI, 'replies'> => ({
      id: item.id,
      text: item.text,
      cursorId: Number(item.cursorId.toString()),
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      Profile: transformProfile(item.Profile),
    });

    const commentResult = commentTempResult.map((item) => ({
      ...transformComment(item),
      repliesCount: Number(item.repliesCount.toString()),
      replies: item.replies.map((reply) => ({
        ...transformComment(reply),
        replies: [],
      })),
    }));

    const lastComment = commentResult[commentResult.length - 1];
    const resultCursorId =
      commentResult.length > 0 && lastComment ? lastComment.cursorId : typeof cursorId === 'number' ? cursorId : null;

    return {
      comments: commentResult,
      total: Number(commentsCountResult?.[0]?.total || '0'),
      cursorId: resultCursorId,
    };
  },

  fetchReplies: async (
    state: FastifyStateI,
    { scrapBookPostId, parentCommentId, cursorId, pageSize }: ScrapBookCommentFetchRepliesI,
  ): Promise<ScrapBookCommentFetchManyResultI> => {
    const selfProfileId = state.profileId;

    const [commentsCountResult, commentTempResult] = await Promise.all([
      prismaPG.$queryRaw<{ total: bigint }[]>`
        SELECT COUNT(DISTINCT c."id") AS "total"
        FROM "port"."ScrapBookComment" c
        INNER JOIN "user"."Profile" u ON u."id" = c."profileId"
        WHERE c."scrapBookPostId" = ${scrapBookPostId}::uuid
          AND c."parentCommentId" = ${parentCommentId}::uuid
          AND c."status" =  ${PostStatusE.ACTIVE}::"feed"."PostStatusE"
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE (
              (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
              OR
              (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
            )
            LIMIT 1
          )
      `,
      prismaPG.$queryRaw<ScrapBookCommentTempResultItemI[]>`
        SELECT
        c."id",
        c."text",
        c."cursorId",
        c."createdAt",
        c."updatedAt",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
      FROM "port"."ScrapBookComment" c
      INNER JOIN "user"."Profile" u ON u."id" = c."profileId"
      WHERE c."scrapBookPostId" = ${scrapBookPostId}::uuid
        AND c."parentCommentId" = ${parentCommentId}::uuid
        AND c."status" =  ${PostStatusE.ACTIVE}::"feed"."PostStatusE"
        ${cursorId ? Prisma.sql`AND c."cursorId" < ${BigInt(cursorId)}` : Prisma.empty}
        AND NOT EXISTS (
          SELECT 1
          FROM "network"."BlockedProfile" b
          WHERE (
            (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
            OR
            (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
          )
          LIMIT 1
        )
      ORDER BY c."cursorId" DESC
      LIMIT ${pageSize}
    `,
    ]);

    const transformProfile = (profile: ProfileItemI) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const transformComment = (item: ScrapBookCommentTempResultItemI): ScrapBookCommentFetchForClientI => ({
      id: item.id,
      text: item.text,
      cursorId: Number(item.cursorId.toString()),
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      Profile: transformProfile(item.Profile),
      replies: [],
    });

    const commentResult: ScrapBookCommentFetchForClientI[] = commentTempResult.map(transformComment);

    const lastComment = commentResult[commentResult.length - 1];
    const resultCursorId =
      commentResult.length > 0 && lastComment ? lastComment.cursorId : typeof cursorId === 'number' ? cursorId : null;

    return {
      comments: commentResult,
      total: Number(commentsCountResult?.[0]?.total || '0'),
      cursorId: resultCursorId,
    };
  },

  createOne: async (
    state: FastifyStateI,
    { parentCommentId, scrapBookPostId, text }: ScrapBookCommentCreateOneI,
  ): Promise<ScrapBookCommentCreateOneResultI> => {
    if (!state.profileId) {
      throw new AppError('AUTH001');
    }

    const [scrapBookPostExists, parentCommentExists] = await Promise.all([
      prismaPG.scrapBookPost.findUnique({
        where: {
          id: scrapBookPostId,
          status: PostStatusE.ACTIVE,
        },
        select: { id: true },
      }),
      parentCommentId
        ? prismaPG.scrapBookComment.findUnique({
            where: {
              id: parentCommentId,
              status: PostStatusE.ACTIVE,
              scrapBookPostId,
              parentCommentId: null,
            },
            select: { id: true },
          })
        : Promise.resolve(null),
    ]);

    if (!scrapBookPostExists) {
      throw new AppError('SCBKCMT001');
    }

    if (parentCommentId && !parentCommentExists) {
      throw new AppError('SCBKCMT001');
    }

    const result = await prismaPG.$transaction(async (tx) => {
      const comment = await tx.scrapBookComment.create({
        data: {
          text: text.trim(),
          status: PostStatusE.ACTIVE,
          ScrapBookPost: {
            connect: { id: scrapBookPostId },
          },
          Profile: {
            connect: { id: state.profileId },
          },
          ...(parentCommentId
            ? {
                Parent: {
                  connect: { id: parentCommentId },
                },
              }
            : {}),
        },
        select: {
          id: true,
          cursorId: true,
        },
      });

      await tx.scrapBookPost.update({
        where: { id: scrapBookPostId },
        data: {
          commentCount: { increment: 1 },
        },
      });

      return comment;
    });

    return {
      id: result.id,
      cursorId: Number(result.cursorId.toString()),
    };
  },

  deleteOne: async (
    state: FastifyStateI,
    filters: Pick<Prisma.ScrapBookCommentWhereUniqueInput, 'id'>,
  ): Promise<void> => {
    const commentResult = await prismaPG.scrapBookComment.findFirst({
      select: {
        id: true,
        parentCommentId: true,
        scrapBookPostId: true,
        ScrapBookPost: {
          select: {
            profileId: true,
          },
        },
      },
      where: {
        OR: [{ profileId: state.profileId }, { ScrapBookPost: { profileId: state.profileId } }],
        id: filters.id,
        status: PostStatusE.ACTIVE,
      },
    });

    if (!commentResult) {
      throw new AppError('SCBKCMT001');
    }

    await prismaPG.$transaction(async (txn) => {
      let countCommentAndReplies = 1;

      if (!commentResult.parentCommentId) {
        const countRepliesResult = await txn.scrapBookComment.count({
          where: {
            parentCommentId: commentResult.id,
            status: PostStatusE.ACTIVE,
          },
        });

        if (countRepliesResult > 0) {
          countCommentAndReplies += countRepliesResult;
          await txn.scrapBookComment.updateMany({
            data: { status: PostStatusE.DELETED },
            where: {
              parentCommentId: commentResult.id,
              status: PostStatusE.ACTIVE,
            },
          });
        }
      }

      await txn.scrapBookComment.update({
        data: { status: PostStatusE.DELETED },
        where: { id: commentResult.id },
      });

      await txn.scrapBookPost.update({
        data: {
          commentCount: {
            decrement: countCommentAndReplies,
          },
        },
        where: {
          id: commentResult.scrapBookPostId,
        },
      });
    });
  },
};
