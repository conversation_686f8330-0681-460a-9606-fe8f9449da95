import { prismaPG } from '@config/db';
import type { ObjStrI } from '@interfaces/common/data';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';
import { PortContributionFetchOneParamsI, PortContributionUpsertOneParamsI } from '@schemas/port/contribution';
import { PortModule } from './port';
import AppError from '@classes/AppError';
import { KeyContributionParamsI } from '@interfaces/common/contribution';
import { isPrimitiveData } from '@utils/data/data';

export const PortContributionModule = {
  fetch: async (state: FastifyStateI, params: PortContributionFetchOneParamsI) => {
    await PortModule.fetchByUnLocode({ unLocode: params.unLocode, dataType: params.dataType });

    const portContributionsResult = await prismaPG.$queryRaw<{ label: string; value: string }[]>`
      SELECT
        pc."label",
        pc."value"
      FROM "port"."PortContribution" pc
      WHERE pc."profileId" = ${state.profileId}::uuid
        AND ${
          params.dataType === 'master'
            ? Prisma.sql`pc."portUnLocode" = ${params.unLocode}`
            : Prisma.sql`pc."portRawDataUnLocode" = ${params.unLocode}`
        }
      ORDER BY pc."createdAt" DESC
    `;

    return portContributionsResult;
  },
  upsertOne: async (state: FastifyStateI, params: PortContributionUpsertOneParamsI): Promise<void> => {
    await PortModule.fetchByUnLocode({ unLocode: params.unLocode, dataType: params.dataType });
    const contributionsMap: KeyContributionParamsI = params.contributions.reduce((acc, curr) => {
      acc[curr.label] = curr.value;
      return acc;
    }, {} as KeyContributionParamsI);

    const existingPortContributionsResult = await prismaPG.$queryRaw<{ id: string; label: string }[]>`
      SELECT
        pc."id",
        pc."label"
      FROM "port"."PortContribution" pc
      WHERE pc."profileId" = ${state.profileId}::uuid
        AND pc."label" IN (${Prisma.join(Object.keys(contributionsMap))})
        AND ${
          params.dataType === 'master'
            ? Prisma.sql`pc."portUnLocode" = ${params.unLocode}`
            : Prisma.sql`pc."portRawDataUnLocode" = ${params.unLocode}`
        }
    `;
    const existingContributionLabelIdMap: ObjStrI = existingPortContributionsResult.reduce((acc, curr) => {
      acc[curr.label] = curr.id;
      return acc;
    }, {} as ObjStrI);

    const createInput: Prisma.PortContributionUncheckedCreateInput[] = [];
    const updateInput: { id: string; value: string }[] = [];

    Object.keys(contributionsMap).forEach((labelKey) => {
      if (!labelKey || typeof labelKey !== 'string') return;
      const contributionValue = contributionsMap[labelKey];
      if (contributionValue == null) return;
      const value = isPrimitiveData(contributionValue) ? String(contributionValue) : JSON.stringify(contributionValue);
      if (!labelKey.trim() || !value.trim() || !state.profileId) return;

      if (existingContributionLabelIdMap[labelKey]) {
        updateInput.push({
          id: existingContributionLabelIdMap[labelKey],
          value,
        });
      } else {
        const newItem: Prisma.PortContributionUncheckedCreateInput = {
          label: labelKey,
          value,
          profileId: state.profileId,
          dataStatus: 'PENDING',
        };

        if (params.dataType === 'master') {
          newItem.portUnLocode = params.unLocode;
          newItem.portRawDataUnLocode = undefined;
        } else {
          newItem.portRawDataUnLocode = params.unLocode;
          newItem.portUnLocode = undefined;
        }

        createInput.push(newItem);
      }
    });

    let countUpserted: number = 0;
    if (createInput.length > 0) {
      if (createInput.some((item) => !item.label || !item.value || !item.profileId)) {
        throw new AppError('PRTCNB005');
      }

      const createResult = await prismaPG.portContribution.createMany({
        data: createInput,
      });
      countUpserted += createResult.count;
    }

    if (updateInput.length > 0) {
      await Promise.all(
        updateInput.map((item) =>
          prismaPG.portContribution.update({
            where: { id: item.id },
            data: {
              value: item.value,
            },
          }),
        ),
      );
      countUpserted += updateInput.length;
    }

    if (countUpserted !== Object.keys(contributionsMap).length) {
      throw new AppError('PRTCNB006');
    }
    return;
  },
};
