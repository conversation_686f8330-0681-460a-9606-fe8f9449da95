import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import { Prisma, AppLog } from '@prisma/mongodb';
import { errorHandler } from '@utils/errors/handler';

export const AppLogModule = {
  fetchById: async (filters: Prisma.AppLogWhereUniqueInput) => {
    try {
      const appLogResult = await prismaMG.appLog.findUnique({
        where: filters,
      });
      if (!appLogResult) {
        throw new AppError('APPLG002');
      }
      return appLogResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  createOne: async (params: Prisma.AppLogUncheckedCreateInput): Promise<Pick<AppLog, 'id'>> => {
    try {
      const appLogResult = await prismaMG.appLog.create({
        data: params,
        select: {
          id: true,
        },
      });
      if (!appLogResult) {
        throw new AppError('APPLG001');
      }
      return appLogResult;
    } catch (error) {
      errorHandler(error);
    }
  },
};
