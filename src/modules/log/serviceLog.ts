import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import { Prisma, ServiceLog } from '@prisma/mongodb';

export const ServiceLogModule = {
  fetchById: async (filters: Prisma.ServiceLogWhereUniqueInput) => {
    try {
      const serviceLogResult = await prismaMG.serviceLog.findUnique({
        where: filters,
      });
      if (!serviceLogResult) {
        throw new AppError('SVCLG002');
      }
      return serviceLogResult;
    } catch (_error) {
      //
    }
  },
  createOne: async (params: Prisma.ServiceLogUncheckedCreateInput): Promise<Pick<ServiceLog, 'id'>> => {
    try {
      const serviceLogResult = await prismaMG.serviceLog.create({
        data: params,
        select: {
          id: true,
        },
      });
      if (!serviceLogResult) {
        throw new AppError('SVCLG001');
      }
      return serviceLogResult;
    } catch (_error) {
      //
    }
  },
};
