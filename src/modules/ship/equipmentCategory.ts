import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import type { IdNameTypeI, NullableI, TotalDataI } from '@interfaces/common/data';
import type { PostgresTxnI } from '@interfaces/common/db';
import type {
  EquipmentCategoryNestedClientI,
  EquipmentCategoryTransformParamsI,
} from '@interfaces/ship/equipmentCategory';
import { Prisma, EquipmentCategory } from '@prisma/postgres';
import type { IdTypeI, PaginationI } from '@schemas/common/common';
import type { EquipmentCategoryFetchForClientI, EquipmentCategoryFetchsertI } from '@schemas/ship/equipmentCategory';

export const EquipmentCategoryModule = {
  fetchById: async (
    { id, dataType }: IdTypeI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EquipmentCategoryNestedClientI> => {
    const equipmentCategoryResultTemp = await txn.$queryRaw<Pick<EquipmentCategory, 'id' | 'name' | 'hasFuelType'>>`
    ${
      dataType === 'master'
        ? Prisma.sql`
          SELECT
            e."id",
            e."name",
            e."hasFuelType"
          FROM
            "ship"."EquipmentCategory" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
        : Prisma.sql`
          SELECT
            e."id",
            e."name",
            e."hasFuelType"
          FROM
            "rawData"."EquipmentCategoryRawData" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
    }
    `;
    if (!equipmentCategoryResultTemp) {
      throw new AppError('EQPCG001');
    }
    return {
      ...equipmentCategoryResultTemp,
      dataType,
    } as EquipmentCategoryNestedClientI;
  },
  fetchForClient: async (
    filtersP: EquipmentCategoryFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<TotalDataI<EquipmentCategoryNestedClientI>> => {
    filtersP.search = filtersP?.search?.trim().toLowerCase();
    const [equipmentCategoriesResult, equipmentCategoriesTotalResult] = await Promise.all([
      prismaPG.$queryRaw<EquipmentCategoryNestedClientI[]>`
      SELECT
        e."id",
        e."name",
        e."hasFuelType",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentCategory" e
      ${filtersP.search ? Prisma.sql`WHERE e."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}

      UNION

      SELECT
        erw."id",
        erw."name",
        erw."hasFuelType",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentCategoryRawData" erw
      ${filtersP.search ? Prisma.sql`WHERE erw."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}
      ORDER BY
        "dataType" ASC,
        "name" ASC
      LIMIT ${pagination.pageSize}
      OFFSET ${pagination.page * pagination.pageSize}
    `,
      prismaPG.$queryRaw<{ total: number }[]>`
      SELECT (
        (SELECT COUNT(*) FROM "ship"."EquipmentCategory" e
         ${filtersP.search ? Prisma.sql`WHERE e."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}) +
        (SELECT COUNT(*) FROM "rawData"."EquipmentCategoryRawData" erw
         ${filtersP.search ? Prisma.sql`WHERE erw."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty})
      )::INTEGER AS total
    `,
    ]);

    return {
      data: equipmentCategoriesResult,
      total: Number(equipmentCategoriesTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async ({ name, hasFuelType }: EquipmentCategoryFetchsertI): Promise<EquipmentCategoryNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const results = await prismaPG.$queryRaw<EquipmentCategoryNestedClientI[]>`
    SELECT * FROM (
      SELECT
        e."id",
        e."name",
        e."hasFuelType",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentCategory" e
      WHERE
        e."name" = ${name}

      UNION

      SELECT
        erw."id",
        erw."name",
        erw."hasFuelType",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentCategoryRawData" erw
      WHERE
        erw."name" = ${name}
    ) AS combinedResult
    ORDER BY
      combinedResult."dataType" ASC,
      combinedResult."name" ASC
    LIMIT 1
  `;

    if (results && results.length > 0) {
      return results[0];
    }

    const equipmentCategoryResultTemp = await prismaPG.equipmentCategoryRawData.create({
      data: {
        name,
        hasFuelType,
      },
      select: {
        id: true,
        name: true,
        hasFuelType: true,
      },
    });

    if (!equipmentCategoryResultTemp) {
      throw new AppError('EQPCG002', 'Failed to create equipment category');
    }

    return {
      ...equipmentCategoryResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as EquipmentCategoryNestedClientI;
  },
  transform: ({
    equipmentCategoryId,
    equipmentCategoryName,
    equipmentCategoryRawDataId,
    equipmentCategoryRawDataName,
  }: EquipmentCategoryTransformParamsI): NullableI<IdNameTypeI> =>
    equipmentCategoryId
      ? { id: equipmentCategoryId, name: equipmentCategoryName, type: 'master' }
      : equipmentCategoryRawDataId
        ? { id: equipmentCategoryRawDataId, name: equipmentCategoryRawDataName, type: 'raw' }
        : null,
};
