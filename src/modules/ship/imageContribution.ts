import { prismaPG } from '@config/db';
import { PAGINATION } from '@consts/common/pagination';
import { PaginationI } from '@schemas/common/common';
import { CoreShipModule } from './ship';
import { ShipImageContribution, Prisma } from '@prisma/postgres';
import { ShipImageFetchForClientParamsI } from '@schemas/ship/contribution';

import { FastifyStateI } from '@interfaces/common/declaration';
import { ShipUploadImagesBulkParamsI } from '@interfaces/ship/contribution';

export const ShipImageContributionModule = {
  fetchForClient: async (params: ShipImageFetchForClientParamsI, pagination: PaginationI = PAGINATION) => {
    await CoreShipModule.fetchByImo({
      imo: params.imo,
      dataType: params.dataType,
    });
    const filter: Prisma.ShipImageContributionWhereInput = {
      dataStatus: {
        in: ['APPROVED', 'SAVED'],
      },
    };
    if (params.dataType === 'master') {
      filter.shipImo = params.imo;
    } else if (params.dataType === 'raw') {
      filter.shipRawDataImo = params.imo;
    }
    const shipImageContributionResult = await prismaPG.shipImageContribution.findMany({
      select: {
        id: true,
        imageUrl: true,
      },
      where: filter,
      orderBy: {
        createdAt: 'desc',
      },
      skip: pagination.page,
      take: pagination.pageSize,
    });
    return shipImageContributionResult;
  },
  uploadImagesBulk: async (
    state: FastifyStateI,
    { fileUrls, imo, dataType }: ShipUploadImagesBulkParamsI,
  ): Promise<Pick<ShipImageContribution, 'id' | 'imageUrl'>[]> => {
    const _shipResult = await CoreShipModule.fetchByImo({ dataType, imo });
    const createInput = fileUrls.map((imageUrl) => {
      const createInputItem = {
        imageUrl,
        profileId: state.profileId,
      } as Prisma.ShipImageContributionUncheckedCreateInput;
      if (dataType === 'master') {
        createInputItem.shipImo = imo;
      } else if (dataType === 'raw') {
        createInputItem.shipRawDataImo = imo;
      }
      return createInputItem;
    });
    const shipImageContributionResult = await prismaPG.shipImageContribution.createManyAndReturn({
      data: createInput,
      select: {
        id: true,
        imageUrl: true,
      },
    });
    return shipImageContributionResult;
  },
};
