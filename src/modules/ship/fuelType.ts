import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { PostgresTxnI } from '@interfaces/common/db';
import { FuelTypeClientI } from '@interfaces/ship/fuelType';
import { Prisma, FuelType } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { FuelTypeFetchForClientI, FuelTypeFetchsertI } from '@schemas/ship/fuelType';

export const FuelTypeModule = {
  fetchById: async ({ id, dataType }: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<FuelTypeClientI> => {
    const fuelTypeResultTemp = await txn.$queryRaw<Pick<FuelType, 'id' | 'name'>>`
      ${
        dataType === 'master'
          ? Prisma.sql`
          SELECT
            f."id",
            f."name"
          FROM
            "ship"."FuelType" f
          WHERE
            f."id" = ${id}::uuid
          LIMIT 1
        `
          : Prisma.sql`
          SELECT
            f."id",
            f."name"
          FROM
            "rawData"."FuelTypeRawData" f
          WHERE
            f."id" = ${id}::uuid
          LIMIT 1
        `
      }
    `;
    if (!fuelTypeResultTemp) {
      throw new AppError('FULTYP001');
    }
    return {
      ...fuelTypeResultTemp,
      dataType: 'master',
    } as FuelTypeClientI;
  },

  fetchForClient: async (
    filtersP: FuelTypeFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: FuelTypeClientI[]; total: number }> => {
    filtersP.search = filtersP.search.trim().toLowerCase();
    const fuelTypesResult = await prismaPG.$queryRaw<FuelTypeClientI[]>`
    SELECT * FROM
      (
        SELECT
          f."id",
          f."name",
          'master' AS "dataType"
        FROM
          "ship"."FuelType" f
        WHERE
          f."name" ILIKE ${`${filtersP.search + '%'}%`}

        UNION ALL
        SELECT
          frw."id",
          frw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."FuelTypeRawData" frw
        WHERE
          frw."name" ILIKE ${`${filtersP.search + '%'}%`}


      ) AS combinedResult
      ORDER BY
        combinedResult."dataType" ASC,
        combinedResult."name" ASC
         LIMIT ${pagination.pageSize}
        OFFSET ${pagination.page}
    `;
    const totalResult = await prismaPG.$queryRaw<{ count: bigint }[]>`
    SELECT COUNT(*) as count FROM
      (
        SELECT 1 FROM
          "ship"."FuelType" f
        WHERE
          f."name" ILIKE ${`${filtersP.search + '%'}%`}

        UNION ALL
        SELECT 1 FROM
          "rawData"."FuelTypeRawData" frw
        WHERE
          frw."name" ILIKE ${`${filtersP.search + '%'}%`}
      ) AS combinedResult
  `;
    return {
      data: fuelTypesResult,
      total: Number(totalResult[0].count),
    };
  },

  fetchsert: async ({ name }: FuelTypeFetchsertI): Promise<FuelTypeClientI> => {
    name = name?.toLowerCase()?.trim();
    const fuelTypeRawResult = await prismaPG.$queryRaw<FuelTypeClientI[]>`
      SELECT * FROM
      (
        SELECT
          f."id",
          f."name",
          'master' AS "dataType"
        FROM
        "ship"."FuelType" f
        WHERE
        f."name" = ${name}

        UNION
        SELECT
          frw."id",
          frw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."FuelTypeRawData" frw
        WHERE
          frw."name" = ${name}

      ) AS combinedResult
      ORDER BY
        combinedResult."dataType" ASC,
        combinedResult."name" ASC
      LIMIT 1
    `;
    if (fuelTypeRawResult && fuelTypeRawResult.length > 0) {
      return fuelTypeRawResult[0];
    }
    const fuelTypeResultTemp = await prismaPG.fuelTypeRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    });
    if (!fuelTypeResultTemp?.id) {
      throw new AppError('FULTYP002');
    }
    return {
      ...fuelTypeResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as FuelTypeClientI;
  },
};
