import type { FirebaseConfigI } from '@navicater/vendor-firebase';
import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import type { GetVendorResultI, VendorI } from '@interfaces/service';
import { Prisma, VendorNameE } from '@prisma/mongodb';
import { decryptJSON } from '@utils/cryptography/encDec';
import { isFilled } from '@utils/data/object';
import { ObjUnknownI } from '@interfaces/common/data';
import Firebase from '@navicater/vendor-firebase';
import Google, { GoogleConfigI } from '@navicater/vendor-google';
import { isFilledString } from '@utils/data/string';

const VendorModule = {
  fetchByName: async (filters: Pick<Prisma.VendorWhereInput, 'name'>): Promise<VendorI> => {
    const select: Prisma.VendorSelect = {
      name: true,
      type: true,
      config: true,
      credsConfig: true,
    };
    const tempResult = await prismaMG.vendor.findFirst({
      where: filters,
      select,
    });
    if (!tempResult) {
      throw new AppError('VND001');
    }
    let config: ObjUnknownI = {};
    if (isFilled(tempResult.config)) {
      config = {
        ...(tempResult.config as ObjUnknownI),
      };
    }
    if (isFilledString(tempResult?.credsConfig)) {
      config = { ...config, ...(decryptJSON(tempResult.credsConfig) as ObjUnknownI) };
    }
    const vendorResult: VendorI = {
      ...tempResult,
      config,
    };
    return vendorResult;
  },
  getVendor: async (name: VendorNameE): Promise<GetVendorResultI> => {
    const vendorResult: VendorI = await VendorModule.fetchByName({ name });
    const config: ObjUnknownI = vendorResult.config as ObjUnknownI;

    switch (name) {
      case 'FIREBASE': {
        return {
          name,
          config: config as FirebaseConfigI,
          instance: new Firebase(config as FirebaseConfigI),
        };
      }
      case 'GOOGLE': {
        return {
          name,
          config: config as GoogleConfigI,
          instance: new Google(config as GoogleConfigI),
        };
      }
      default: {
        throw new AppError('VND001');
      }
    }
  },
};

export default VendorModule;
