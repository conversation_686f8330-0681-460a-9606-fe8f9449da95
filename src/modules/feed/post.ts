import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { PostDataClientI, PostExternalClientI, PostFetchManyResultI } from '@interfaces/feed/post';
import { ProfileExternalI } from '@interfaces/user/profile';
import { PostStatusE, Prisma } from '@prisma/postgres';
import { RouteParamsI } from '@schemas/common/common';
import type { PostCreateOneI, PostFetchManyI, PostSearchSchemaI, PostUpdateOneI } from '@schemas/feed/post';
import { omit } from '@utils/data/object';
import { errorHandler } from '@utils/errors/handler';

const PostModule = {
  createOne: async (state: FastifyStateI, { files, caption }: PostCreateOneI) => {
    try {
      const profileId = state.profileId;
      const postResult = await prismaPG.post.create({
        data: {
          caption,
          profileId,
          Media: files?.length
            ? {
                createMany: {
                  data: files.map((fileItem) => ({
                    caption: fileItem?.caption,
                    profileId,
                    fileUrl: fileItem.fileUrl,
                    fileExtension: fileItem.extension,
                  })),
                },
              }
            : undefined,
        },
        include: { Media: true },
      });
      if (!postResult) {
        throw new AppError('POST002');
      }
      const postForClientResult = { ...omit(postResult, ['cursorId']), cursorId: Number(postResult['cursorId']) };
      return postForClientResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchManySearchGlobal: async (state: FastifyStateI, body: PostSearchSchemaI) => {
    try {
      const selfProfileId = state.profileId;
      const { search, page, pageSize } = body;

      const offset = page * pageSize;
      const searchTerm = `%${search.toLowerCase()}%`;

      const countResult = await prismaPG.$queryRaw<[{ count: bigint }]>`
        SELECT COUNT(DISTINCT p."id") as count
        FROM "feed"."Post" p
        INNER JOIN "user"."Profile" u
        ON p."profileId" = u."id"
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN "feed"."PostMedia" pm ON p."id" = pm."postId"
        LEFT JOIN "network"."BlockedProfile" b1
          ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
          ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
        WHERE p."status" = 'ACTIVE'
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
        AND (
          LOWER(p."caption") LIKE ${searchTerm}
          OR LOWER(pm."caption") LIKE ${searchTerm}
          OR LOWER(u."name") LIKE ${searchTerm}
          OR LOWER(u."designationText") LIKE ${searchTerm}
          OR LOWER(u."entityText") LIKE ${searchTerm}
        )
      `;

      const total = Number(countResult[0]?.count || 0);

      const searchPostsResult: PostDataClientI[] = await prismaPG.$queryRaw`
        WITH ranked_posts AS (
          SELECT DISTINCT p."id",
          CASE
            WHEN LOWER(p."caption") LIKE ${searchTerm} THEN 1
            WHEN LOWER(u."name") LIKE ${searchTerm} THEN 2
            WHEN LOWER(u."designationText") LIKE ${searchTerm} THEN 3
            WHEN LOWER(u."entityText") LIKE ${searchTerm} THEN 4
            WHEN LOWER(pm."caption") LIKE ${searchTerm} THEN 5
            ELSE 6
          END AS relevance_score,
          p."createdAt"
          FROM "feed"."Post" p
          INNER JOIN "user"."Profile" u
            ON p."profileId" = u."id"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          LEFT JOIN "feed"."PostMedia" pm ON p."id" = pm."postId"
          LEFT JOIN "network"."BlockedProfile" b1
            ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
          LEFT JOIN "network"."BlockedProfile" b2
            ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
          WHERE p."status" = 'ACTIVE'
          AND b1."blockerId" IS NULL
          AND b2."blockerId" IS NULL
          AND (
            LOWER(p."caption") LIKE ${searchTerm}
            OR LOWER(pm."caption") LIKE ${searchTerm}
            OR LOWER(u."name") LIKE ${searchTerm}
            OR LOWER(u."designationText") LIKE ${searchTerm}
            OR LOWER(u."entityText") LIKE ${searchTerm}
          )
          ORDER BY relevance_score, p."createdAt" DESC
          LIMIT ${pageSize}
          OFFSET ${offset}
        )
        SELECT
        p."id" AS "id",
        p."cursorId" AS "cursorId",
        p."caption" AS "caption",
        CASE
          WHEN LENGTH(p."caption") > 250 THEN SUBSTRING(p."caption", 1, 250)
          ELSE p."caption"
        END AS "shortCaption",
        (LENGTH(p."caption") > 250) AS "isCaptionTruncated",
        p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" pr
          WHERE pr."postId" = p."id"
          AND pr."profileId" = ${selfProfileId}::uuid
        ) AS "isLiked",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl",
              'extension', m."fileExtension"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
        FROM ranked_posts rp
        INNER JOIN "feed"."Post" p ON rp."id" = p."id"
        INNER JOIN "user"."Profile" u
          ON p."profileId" = u."id"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        ORDER BY rp.relevance_score, rp."createdAt" DESC
      `;

      const data = searchPostsResult?.length
        ? searchPostsResult.map((item) => ({
            ...item,
            caption: item.shortCaption,
            isCaptionTruncated: item.isCaptionTruncated,
            cursorId: Number(item.cursorId),
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile?.designationAlternativeId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText,
                    dataType: 'master',
                  }
                : item.Profile?.designationRawDataId
                  ? {
                      id: item.Profile.designationRawDataId,
                      name: item.Profile.designationText,
                      dataType: 'raw',
                    }
                  : null,
              entity: item.Profile?.entityId
                ? {
                    id: item.Profile.entityId,
                    name: item.Profile.entityText,
                    dataType: 'master',
                  }
                : item.Profile?.entityRawDataId
                  ? {
                      id: item.Profile.entityRawDataId,
                      name: item.Profile.entityText,
                      dataType: 'raw',
                    }
                  : null,
            } as ProfileExternalI,
          }))
        : [];

      return {
        data,
        total,
      };
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchMany: async ({ profileId, pagination }: PostFetchManyI): Promise<PostFetchManyResultI> => {
    try {
      const postFetchManyResult: PostFetchManyResultI = {
        posts: [],
      };
      let cursorId: number | null = null;

      if (typeof pagination.cursorId === 'number' && pagination.cursorId > 0) {
        cursorId = Number(pagination.cursorId);
      }

      const postsResult: PostDataClientI[] = await prismaPG.$queryRaw`
        SELECT p."id" AS "id",
        p."caption" AS "caption",
        CASE
          WHEN length(p."caption") > 100 THEN LEFT(p."caption", 100)  || '...'
          ELSE p."caption"
          END AS "caption",
          length(p."caption") > 100 AS "isCaptionTruncated",
        p."cursorId" AS "cursorId",
        p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" r
          WHERE r."postId" = p."id"
          AND r."profileId" = ${profileId}::uuid
        ) AS "isLiked",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
        FROM "feed"."Post" p
        INNER JOIN "user"."Profile" u
        ON p."profileId" = u."id"
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${profileId}::uuid
        AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${profileId}::uuid
        WHERE p."status" = 'ACTIVE'
        ${cursorId ? Prisma.sql`AND p."cursorId" < ${cursorId}` : Prisma.empty}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
        ORDER BY p."createdAt" DESC
        LIMIT ${pagination.pageSize}
      `;

      if (postsResult?.length) {
        postFetchManyResult.cursorId = Number(postsResult?.[postsResult?.length - 1].cursorId);
        postFetchManyResult.posts.push(
          ...postsResult.map((item) => ({
            ...item,
            cursorId: Number(item.cursorId),
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile?.designationAlternativeId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText,
                    dataType: 'master',
                  }
                : item.Profile?.designationRawDataId
                  ? {
                      id: item.Profile.designationAlternativeId,
                      name: item.Profile.designationText,
                      dataType: 'raw',
                    }
                  : null,
              entity: item.Profile?.entityId
                ? {
                    id: item.Profile.entityId,
                    name: item.Profile.entityText,
                    dataType: 'master',
                  }
                : item.Profile?.entityRawDataId
                  ? {
                      id: item.Profile.entityRawDataId,
                      name: item.Profile.entityText,
                      dataType: 'raw',
                    }
                  : null,
            } as ProfileExternalI,
          })),
        );
      }
      return postFetchManyResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  deleteOne: async (state: FastifyStateI, filtersP: RouteParamsI): Promise<void> => {
    try {
      const deletedPostResult = await prismaPG.post.update({
        where: { id: filtersP.id, profileId: state.profileId },
        data: { status: PostStatusE.DELETED },
        select: { id: true },
      });

      if (!deletedPostResult) {
        throw new AppError('POST007');
      }
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchOne: async (state: FastifyStateI, filtersP: RouteParamsI): Promise<Omit<PostExternalClientI, 'cursorId'>> => {
    try {
      const selfProfileId = state.profileId;
      const postResultArrTemp: PostDataClientI[] = await prismaPG.$queryRaw`
      SELECT p."id" AS "id",
      p."caption" AS "caption",
      p."reactionsCount" AS "reactionsCount",
      p."totalCommentsCount" AS "totalCommentsCount",
      EXISTS (
        SELECT 1 FROM "feed"."PostReaction" pr
        WHERE pr."postId" = p."id"
        AND pr."profileId" = ${selfProfileId}::uuid
      ) AS "isLiked",
      p."createdAt" AS "createdAt",
      (
        SELECT json_agg(
          json_build_object(
            'caption', m."caption",
            'fileUrl', m."fileUrl"
          )
        )
        FROM "feed"."PostMedia" m
        WHERE m."postId" = p."id"
      ) AS "Media",
      json_build_object(
        'id', u."id",
        'name', u."name",
        'avatar', u."avatar",
        'designationText', u."designationText",
        'designationAlternativeId', u."designationAlternativeId",
        'designationRawDataId', u."designationRawDataId",
        'entityText', u."entityText",
        'entityId', u."entityId",
        'entityRawDataId', u."entityRawDataId"
      ) AS "Profile"
      FROM "feed"."Post" p
      INNER JOIN "user"."Profile" u
      ON p."profileId" = u."id"
      AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      WHERE p."status" = 'ACTIVE'
      AND p."id" = ${filtersP.id}::uuid
    `;
      const postResultTemp = postResultArrTemp?.[0];
      const postResult: PostExternalClientI = {
        ...postResultTemp,
        Profile: {
          id: postResultTemp.Profile.id,
          name: postResultTemp.Profile.name,
          avatar: postResultTemp.Profile.avatar,
          designation: postResultTemp.Profile?.designationAlternativeId
            ? {
                id: postResultTemp.Profile.designationAlternativeId,
                name: postResultTemp.Profile.designationText,
                dataType: 'master',
              }
            : postResultTemp.Profile?.designationRawDataId
              ? {
                  id: postResultTemp.Profile.designationAlternativeId,
                  name: postResultTemp.Profile.designationText,
                  dataType: 'raw',
                }
              : null,
          entity: postResultTemp.Profile?.entityId
            ? {
                id: postResultTemp.Profile.entityId,
                name: postResultTemp.Profile.entityText,
                dataType: 'master',
              }
            : postResultTemp.Profile?.entityRawDataId
              ? {
                  id: postResultTemp.Profile.entityRawDataId,
                  name: postResultTemp.Profile.entityText,
                  dataType: 'raw',
                }
              : null,
        },
      };
      if (!postResult) {
        throw new AppError('POST001');
      }
      return postResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchProfilePosts: async ({ profileId, pagination }: PostFetchManyI) => {
    try {
      const postFetchManyResult: PostFetchManyResultI = {
        posts: [],
      };
      let cursorId: number | null = null;
      if (typeof pagination.cursorId === 'number' && pagination.cursorId > 0) {
        cursorId = Number(pagination.cursorId);
      }
      const userPostResult: PostDataClientI[] = await prismaPG.$queryRaw`
  SELECT
    p."id" AS "id",
    p."cursorId" AS "cursorId",
    p."caption" AS "caption",
    CASE
      WHEN length(p."caption") > 100 THEN LEFT(p."caption", 100) || '...'
      ELSE p."caption"
    END AS "captionTruncated",
    length(p."caption") > 100 AS "isCaptionTruncated",
    p."reactionsCount" AS "reactionsCount",
    p."totalCommentsCount" AS "totalCommentsCount",
    EXISTS (
      SELECT 1 FROM "feed"."PostReaction" pr
      WHERE pr."postId" = p."id"
        AND pr."profileId" = ${profileId}::uuid
    ) AS "isLiked",
    p."createdAt" AS "createdAt",
    COALESCE(
      (
        SELECT json_agg(
          json_build_object(
            'caption', m."caption",
            'fileUrl', m."fileUrl"
          )
        )
        FROM "feed"."PostMedia" m
        WHERE m."postId" = p."id"
      ), '[]'::json
    ) AS "Media",
    json_build_object(
      'id', u."id",
      'name', u."name",
      'avatar', u."avatar",
      'designationText', u."designationText",
      'designationAlternativeId', u."designationAlternativeId",
      'designationRawDataId', u."designationRawDataId",
      'entityText', u."entityText",
      'entityId', u."entityId",
      'entityRawDataId', u."entityRawDataId"
    ) AS "Profile"
  FROM "feed"."Post" p
  INNER JOIN "user"."Profile" u
    ON p."profileId" = u."id"
    AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
  WHERE p."status" = 'ACTIVE'
    AND p."profileId" = ${profileId}::uuid
    ${cursorId ? Prisma.sql`AND p."cursorId" < ${cursorId}` : Prisma.empty}
  ORDER BY p."createdAt" DESC
  LIMIT ${pagination.pageSize}
`;

      if (userPostResult?.length) {
        postFetchManyResult.posts.push(
          ...userPostResult.map((item) => ({
            ...item,
            cursorId: Number(item.cursorId),
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile?.designationAlternativeId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText,
                    dataType: 'master',
                  }
                : item.Profile?.designationRawDataId
                  ? {
                      id: item.Profile.designationAlternativeId,
                      name: item.Profile.designationText,
                      dataType: 'raw',
                    }
                  : null,
              entity: item.Profile?.entityId
                ? {
                    id: item.Profile.entityId,
                    name: item.Profile.entityText,
                    dataType: 'master',
                  }
                : item.Profile?.entityRawDataId
                  ? {
                      id: item.Profile.entityRawDataId,
                      name: item.Profile.entityText,
                      dataType: 'raw',
                    }
                  : null,
            } as ProfileExternalI,
          })),
        );
        postFetchManyResult.cursorId = Number(userPostResult?.[userPostResult?.length - 1].cursorId);
      }
      return postFetchManyResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchFullPost: async (postId: string, profileId: string): Promise<PostDataClientI> => {
    try {
      const post = await prismaPG.$queryRaw<PostDataClientI[]>`
      SELECT
        p."id" AS "id",
        p."caption" AS "caption",
          p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" pr
          WHERE pr."postId" = p."id"
          AND pr."profileId" = ${profileId}::uuid
        ) AS "isLiked",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
      FROM "feed"."Post" p
      INNER JOIN "user"."Profile" u
        ON p."profileId" = u."id"
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      WHERE p."id" = ${postId}::uuid
      AND p."status" = 'ACTIVE'
    `;

      return post[0];
    } catch (error) {
      errorHandler(error);
    }
  },
  updateOne: async (state: FastifyStateI, { postId, caption, files }: PostUpdateOneI) => {
    try {
      const profileId = state.profileId;
      const existingPost = await prismaPG.post.findFirst({
        where: {
          id: postId,
          profileId,
          status: 'ACTIVE',
        },
        include: { Media: true },
      });
      if (!existingPost) {
        throw new AppError('POST003');
      }
      const updatedPost = await prismaPG.post.update({
        where: { id: postId },
        data: {
          caption,
          Media:
            files !== undefined
              ? {
                  deleteMany: {},
                  ...(files.length > 0 && {
                    createMany: {
                      data: files.map((fileItem) => ({
                        caption: fileItem?.caption,
                        profileId,
                        fileUrl: fileItem.fileUrl,
                        fileExtension: fileItem.extension,
                      })),
                    },
                  }),
                }
              : undefined,
        },
        include: { Media: true },
      });
      if (!updatedPost) {
        throw new AppError('POST004');
      }
      const postForClientResult = { ...omit(updatedPost, ['cursorId']), cursorId: Number(updatedPost['cursorId']) };
      return postForClientResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchFullCaption: async (postId: string): Promise<{ caption: string }> => {
    try {
      const post = await prismaPG.post.findUnique({
        where: { id: postId, status: PostStatusE.ACTIVE },
        select: { caption: true },
      });

      if (!post) {
        throw new AppError('POST001');
      }

      return { caption: post.caption };
    } catch (error) {
      errorHandler(error);
    }
  },
};

export default PostModule;
