import { prismaPG } from '@config/db';
import { TotalDataI } from '@interfaces/common/data';
import { TotalI } from '@interfaces/common/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { PeopleI, PeopleRawI } from '@interfaces/user/profile';
import { Prisma } from '@prisma/postgres';
import { PeopleFetchPeopleOnPortI, PeopleFetchPeopleOnShipI } from '@schemas/career/people';

export const PeopleModule = {
  fetchPeopleOnShip: async (
    state: FastifyStateI,
    { imo, dataType, page, pageSize }: PeopleFetchPeopleOnShipI,
  ): Promise<TotalDataI<PeopleI>> => {
    const selfProfileId = state.profileId;
    const [peopleRawResult, totalResult] = await Promise.all([
      prismaPG.$queryRaw<PeopleRawI[]>`
          SELECT
           json_build_object(
            'id', u."id",
            'name', u."name",
            'avatar', u."avatar",
            'designationText', u."designationText",
            'designationAlternativeId', u."designationAlternativeId",
            'designationRawDataId', u."designationRawDataId",
            'entityText', u."entityText",
            'entityId', u."entityId",
            'entityRawDataId', u."entityRawDataId",
            'isConnected', EXISTS (
              SELECT 1 FROM "network"."Connection" c
              WHERE c."profileId" = ${selfProfileId}::uuid
              AND c."connectedId" = u."id"
            )
          ) AS "Profile"
          FROM "career"."ExperienceShip" s
          INNER JOIN
          "user"."Profile" u
            ON u."id" = s."profileId"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE
          ${
            dataType === 'master'
              ? Prisma.sql`
            s."shipImo" = ${imo}
            `
              : Prisma.sql`
            s."shipRawDataImo" = ${imo}
            `
          }
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE
            (
              b."blockerId" = ${selfProfileId}::uuid
              AND
              b."blockedId" = s."profileId"
            )
            OR
            (
              b."blockerId" = s."profileId"
              AND
              b."blockedId" = ${selfProfileId}::uuid
            )
          )
          ORDER BY
          s."createdAt" DESC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
        `,
      prismaPG.$queryRaw<TotalI>`
          SELECT
            COUNT(*)::int AS total
          FROM "career"."ExperienceShip" s
          INNER JOIN
          "user"."Profile" u
          ON u."id" = s."profileId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE
          ${
            dataType === 'master'
              ? Prisma.sql`
            s."shipImo" = ${imo}
            `
              : Prisma.sql`
            s."shipRawDataImo" = ${imo}
            `
          }
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE
            (
              b."blockerId" = ${selfProfileId}::uuid
              AND
              b."blockedId" = s."profileId"
            )
            OR
            (
              b."blockerId" = s."profileId"
              AND
              b."blockedId" = ${selfProfileId}::uuid
            )
          )
        `,
    ]);
    const peopleResult: PeopleI[] = [];
    if (peopleRawResult?.length) {
      peopleResult.push(
        ...peopleRawResult.map(
          (people) =>
            ({
              id: people.id,
              name: people.name,
              avatar: people.avatar,
              isConnected: people.isConnected,
              designation: people?.designationAlternativeId
                ? {
                    id: people.designationAlternativeId,
                    name: people.designationText,
                    dataType: 'master',
                  }
                : {
                    id: people.designationRawDataId,
                    name: people.designationText,
                    dataType: 'raw',
                  },
              entity: people?.entityId
                ? {
                    id: people.entityId,
                    name: people.entityText,
                    dataType: 'master',
                  }
                : {
                    id: people.entityRawDataId,
                    name: people.entityText,
                    dataType: 'raw',
                  },
            }) as PeopleI,
        ),
      );
    }
    return { data: peopleResult, total: totalResult?.total };
  },
  fetchPeopleOnPort: async (
    state: FastifyStateI,
    { unLocode, dataType, page, pageSize }: PeopleFetchPeopleOnPortI,
  ): Promise<TotalDataI<PeopleI>> => {
    const selfProfileId = state.profileId;
    const [peopleRawResult, totalResult] = await Promise.all([
      prismaPG.$queryRaw<PeopleRawI[]>`
          SELECT
           json_build_object(
            'id', u."id",
            'name', u."name",
            'avatar', u."avatar",
            'designationText', u."designationText",
            'designationAlternativeId', u."designationAlternativeId",
            'designationRawDataId', u."designationRawDataId",
            'entityText', u."entityText",
            'entityId', u."entityId",
            'entityRawDataId', u."entityRawDataId",
            'isConnected', EXISTS (
              SELECT 1 FROM "network"."Connection" c
              WHERE c."profileId" = ${selfProfileId}::uuid
              AND c."connectedId" = u."id"
            )
          ) AS "Profile"
          FROM "port"."PortVisitor" v
          INNER JOIN
          "user"."Profile" u
          ON u."id" = v."profileId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE
          ${
            dataType === 'master'
              ? Prisma.sql`
            v."portUnLocode" = ${unLocode}
            `
              : Prisma.sql`
            v."portRawDataUnLocode" = ${unLocode}
            `
          }
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE
            (
              b."blockerId" = ${selfProfileId}::uuid
              AND
              b."blockedId" = v."profileId"
            )
            OR
            (
              b."blockerId" = v."profileId"
              AND
              b."blockedId" = ${selfProfileId}::uuid
            )
          )
          ORDER BY
          v."createdAt" DESC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
        `,
      prismaPG.$queryRaw<TotalI>`
          SELECT
            COUNT(*)::int AS total
          FROM "port"."PortVisitor" v
          INNER JOIN
          "user"."Profile" u
          ON u."id" = v."profileId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE
          ${
            dataType === 'master'
              ? Prisma.sql`
            v."portUnLocode" = ${unLocode}
            `
              : Prisma.sql`
            v."portRawDataUnLocode" = ${unLocode}
            `
          }
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE
            (
              b."blockerId" = ${selfProfileId}::uuid
              AND
              b."blockedId" = v."profileId"
            )
            OR
            (
              b."blockerId" = v."profileId"
              AND
              b."blockedId" = ${selfProfileId}::uuid
            )
          )
        `,
    ]);
    const peopleResult: PeopleI[] = [];
    if (peopleRawResult?.length) {
      peopleResult.push(
        ...peopleRawResult.map(
          (people) =>
            ({
              id: people.id,
              name: people.name,
              avatar: people.avatar,
              isConnected: people.isConnected,
              designation: people?.designationAlternativeId
                ? {
                    id: people.designationAlternativeId,
                    name: people.designationText,
                    dataType: 'master',
                  }
                : {
                    id: people.designationRawDataId,
                    name: people.designationText,
                    dataType: 'raw',
                  },
              entity: people?.entityId
                ? {
                    id: people.entityId,
                    name: people.entityText,
                    dataType: 'master',
                  }
                : {
                    id: people.entityRawDataId,
                    name: people.entityText,
                    dataType: 'raw',
                  },
            }) as PeopleI,
        ),
      );
    }
    return { data: peopleResult, total: totalResult?.total };
  },
};
