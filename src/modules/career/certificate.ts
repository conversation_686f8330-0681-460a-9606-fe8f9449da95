import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import ServiceModule from '@modules/storage';
import {
  ProfileCertificateCreateForClientI,
  ProfileCertificateCreateOneDataI,
  ProfileCertificateCreateOneParamsI,
  ProfileCertificateForExternalClientI,
  ProfileCertificateForInternalClientI,
  ProfileCertificateUpdateOneParamsI,
} from '@interfaces/career/certificate';
import Company from '@modules/company';
import { Prisma, ProfileCertificate } from '@prisma/postgres';
import { ProfileCertificateFetchForClientI } from '@schemas/career/certificate';
import { separateMasterAndRawData, uniqueArrayObj } from '@utils/data/array';
import { SkillModule } from './skill';
import { isFilled } from '@utils/data/object';
import { SkillNestedClientI } from '@interfaces/company/skill';
import { CertificateCourseNestedClientI } from '@interfaces/company/certificateCourse';
import { EntityNestedClientI } from '@interfaces/company/entity';
import { IdTypeI, IdTypeMapI } from '@schemas/common/common';

export const CertificateModule = {
  createOne: async (
    state: FastifyStateI,
    params: ProfileCertificateCreateOneParamsI,
  ): Promise<ProfileCertificateCreateForClientI> => {
    const [entityClientResult, certificateCourseClientResult] = await Promise.all([
      Company.EntityModule.fetchById(params.institute),
      Company.CertificateCourseModule.fetchById(params.certificateCourse),
    ]);
    const masterSkills: string[] = [];
    const rawDataSkills: string[] = [];
    if (params?.skills?.length) {
      const { master, rawData } = separateMasterAndRawData(params?.skills);
      masterSkills.push(...master);
      rawDataSkills.push(...rawData);
      const { countMasterSkills, countRawDataSkills } = await Company.SkillModule.count(masterSkills, rawDataSkills);
      if (!(masterSkills.length === countMasterSkills && rawDataSkills.length === countRawDataSkills)) {
        throw new AppError('PFCRT005');
      }
    }
    const toCreateProfileCertificateData: ProfileCertificateCreateOneDataI = {
      profileId: state.profileId,
      fromDate: params.fromDate,
      untilDate: params.untilDate,
      fileUrl: params.fileUrl || null,
    };
    if (entityClientResult.dataType === 'master') {
      toCreateProfileCertificateData.entityId = entityClientResult.id;
    } else if (entityClientResult.dataType === 'raw') {
      toCreateProfileCertificateData.entityRawDataId = entityClientResult.id;
    }
    if (certificateCourseClientResult.dataType === 'master') {
      toCreateProfileCertificateData.certificateCourseId = certificateCourseClientResult.id;
    } else if (certificateCourseClientResult.dataType === 'raw') {
      toCreateProfileCertificateData.certificateCourseRawDataId = certificateCourseClientResult.id;
    }
    const updateProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {};
    switch (certificateCourseClientResult.type) {
      case 'STATUTORY': {
        updateProfileMetaParams.statutoryCertCount = {
          increment: 1,
        };
        break;
      }
      case 'VALUE_ADDED': {
        updateProfileMetaParams.valueAddedCertCount = {
          increment: 1,
        };
        break;
      }
      default:
        break;
    }
    const [profileCertificateResult, _profileMetaResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.profileCertificate.create({
            data: toCreateProfileCertificateData,
            select: { id: true, fileUrl: true },
          }),
          isFilled(updateProfileMetaParams)
            ? txn.profileMeta.update({
                select: { profileId: true },
                data: updateProfileMetaParams,
                where: { profileId: state.profileId },
              })
            : null,
        ]),
    );
    if (!profileCertificateResult) {
      throw new AppError('PFCRT002');
    }
    if (masterSkills?.length || rawDataSkills?.length) {
      const _profileSkillResult = await SkillModule.createManyWithEntityCertificate({
        profileId: state.profileId,
        masterSkills,
        rawDataSkills,
        entity: entityClientResult,
        certificate: certificateCourseClientResult,
      });
    }
    return profileCertificateResult;
  },
  deleteOne: async (state: FastifyStateI, { id }: Pick<ProfileCertificate, 'id'>): Promise<void> => {
    const [profileMetaResult, profileCertificateResult] = await Promise.all([
      prismaPG.profileMeta.findUnique({
        select: {
          statutoryCertCount: true,
          valueAddedCertCount: true,
        },
        where: {
          profileId: state.profileId,
        },
      }),
      prismaPG.profileCertificate.findUnique({
        select: {
          id: true,
          fileUrl: true,
          CertificateCourse: {
            select: {
              type: true,
            },
          },
          CertificateCourseRawData: {
            select: {
              type: true,
            },
          },
        },
        where: { id },
      }),
    ]);
    if (!profileCertificateResult) {
      throw new AppError('PFCRT001');
    }

    if (profileCertificateResult.fileUrl) {
      try {
        await ServiceModule.CoreStorageModule.deleteFile({ fileUrl: profileCertificateResult.fileUrl });
      } catch (_error) {
        // console.error(`Failed to delete certificate file ${profileCertificateResult.fileUrl}:`, error);
      }
    }

    const updateProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {};
    switch (
      profileCertificateResult?.CertificateCourse?.type ||
      profileCertificateResult?.CertificateCourseRawData?.type
    ) {
      case 'STATUTORY': {
        if (profileMetaResult?.statutoryCertCount > 0) {
          updateProfileMetaParams.statutoryCertCount = {
            decrement: 1,
          };
        }
        break;
      }
      case 'VALUE_ADDED': {
        if (profileMetaResult?.valueAddedCertCount > 0) {
          updateProfileMetaParams.valueAddedCertCount = {
            decrement: 1,
          };
        }
        break;
      }
      default:
        break;
    }
    const [_profileMetaResult, deletedProfileCertificateResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          isFilled(updateProfileMetaParams)
            ? txn.profileMeta.update({
                data: updateProfileMetaParams,
                where: {
                  profileId: state.profileId,
                },
              })
            : null,
          txn.profileCertificate.delete({
            select: { id: true },
            where: { id },
          }),
        ]),
    );

    if (!deletedProfileCertificateResult) {
      throw new AppError('PFCRT010');
    }
    return;
  },
  updateOne: async (
    params: ProfileCertificateUpdateOneParamsI,
    filter: Pick<Prisma.ProfileCertificateWhereInput, 'id'>,
  ): Promise<void> => {
    const profileCertificateResult = await prismaPG.profileCertificate.findUnique({
      where: {
        id: String(filter.id),
      },
      select: {
        id: true,
        profileId: true,
        entityId: true,
        entityRawDataId: true,
        certificateCourseId: true,
        certificateCourseRawDataId: true,
        fileUrl: true,
      },
    });
    const [entityClientResult, certificateCourseClientResult] = await Promise.all([
      params.institute?.id?.length
        ? Company.EntityModule.fetchById(params.institute)
        : params?.skillsToAdd?.length
          ? Company.EntityModule.fetchById(
              profileCertificateResult?.entityId?.length
                ? { id: profileCertificateResult.entityId, dataType: 'master' }
                : { id: profileCertificateResult.entityRawDataId, dataType: 'raw' },
            )
          : null,

      params.certificateCourse?.id?.length
        ? Company.CertificateCourseModule.fetchById(params.certificateCourse)
        : params?.skillsToAdd?.length
          ? Company.CertificateCourseModule.fetchById(
              profileCertificateResult?.certificateCourseId?.length
                ? { id: profileCertificateResult.certificateCourseId, dataType: 'master' }
                : { id: profileCertificateResult.certificateCourseRawDataId, dataType: 'raw' },
            )
          : null,
    ]);
    if (params.skillsToDelete?.length) {
      params.skillsToDelete = uniqueArrayObj(params.skillsToDelete);
      const existingSkillsToDeleteResult: IdTypeI[] = await SkillModule.fetchSpecificForEntityCertificate({
        entity: {
          id: entityClientResult.id,
          name: entityClientResult.name,
          dataType: entityClientResult.dataType,
        },
        certificateCourse: {
          id: certificateCourseClientResult.id,
          name: certificateCourseClientResult.name,
          dataType: certificateCourseClientResult.dataType,
          type: certificateCourseClientResult.type,
        },
        profileId: String(profileCertificateResult.profileId),
        idTypes: params.skillsToDelete,
      });
      if (existingSkillsToDeleteResult?.length) {
        const _deletedCountResult = await SkillModule.deleteManyForEntityCertificate({
          idTypes: existingSkillsToDeleteResult,
          profileId: profileCertificateResult.profileId,
          certificateCourse: certificateCourseClientResult,
          entity: entityClientResult,
        });
      }
    }
    if (params.skillsToAdd?.length) {
      params.skillsToAdd = uniqueArrayObj(params.skillsToAdd);
      const existingSkillsResult: IdTypeI[] = await SkillModule.fetchSpecificForEntityCertificate({
        entity: {
          id: entityClientResult.id,
          name: entityClientResult.name,
          dataType: entityClientResult.dataType,
        },
        certificateCourse: {
          id: certificateCourseClientResult.id,
          name: certificateCourseClientResult.name,
          type: certificateCourseClientResult.type,
          dataType: certificateCourseClientResult.dataType,
        },
        profileId: profileCertificateResult.profileId,
        idTypes: params.skillsToAdd,
      });
      const existingSkillsMapResult: IdTypeMapI = existingSkillsResult.reduce((acc, curr) => {
        acc[curr.id] = curr;
        return acc;
      }, {} as IdTypeMapI);

      const masterSkills: string[] = [];
      const rawDataSkills: string[] = [];
      params.skillsToAdd.forEach((skillItem) => {
        if (!existingSkillsMapResult?.[skillItem.id]?.id) {
          if (skillItem.dataType === 'master') {
            masterSkills.push(skillItem.id);
          } else {
            rawDataSkills.push(skillItem.id);
          }
        }
      });
      const _profileSkillResult = await SkillModule.createManyWithEntityCertificate({
        masterSkills,
        rawDataSkills,
        certificate: certificateCourseClientResult,
        entity: entityClientResult,
        profileId: profileCertificateResult.profileId,
      });
    }
    const toUpdateInput: Prisma.ProfileCertificateUncheckedUpdateInput = {};
    if (params?.untilDate !== undefined) {
      toUpdateInput.untilDate = params.untilDate;
    }
    if (isFilled(params?.institute)) {
      if (params.institute.dataType === 'master') {
        toUpdateInput.entityId = params.institute.id;
      } else {
        toUpdateInput.entityRawDataId = params.institute.id;
      }
    }
    if (isFilled(params?.certificateCourse)) {
      if (params.certificateCourse.dataType === 'master') {
        toUpdateInput.certificateCourseId = params.certificateCourse.id;
      } else {
        toUpdateInput.certificateCourseRawDataId = params.certificateCourse.id;
      }
    }
    if (params?.fromDate) {
      toUpdateInput.fromDate = params.fromDate;
    }
    if (params?.untilDate) {
      toUpdateInput.untilDate = params.untilDate;
    }
    if (params?.file?.opr) {
      if (params.file.opr === 'CREATE' || params.file.opr === 'UPDATE') {
        if (params.file.opr === 'UPDATE' && profileCertificateResult?.fileUrl) {
          try {
            await ServiceModule.CoreStorageModule.deleteFile({ fileUrl: profileCertificateResult.fileUrl });
          } catch (_error) {
            // console.error(`Failed to delete old certificate file ${profileCertificateResult.fileUrl}:`, error);
          }
        }

        if (params?.file?.fileUrl) {
          toUpdateInput.fileUrl = params.file.fileUrl;
        }
      } else if (params.file.opr === 'DELETE') {
        if (profileCertificateResult?.fileUrl) {
          try {
            await ServiceModule.CoreStorageModule.deleteFile({ fileUrl: profileCertificateResult.fileUrl });
          } catch (_error) {
            // console.error(`Failed to delete certificate file ${profileCertificateResult.fileUrl}:`, error);
          }
        }
        toUpdateInput.fileUrl = null;
      }
    }

    if (isFilled(toUpdateInput)) {
      await prismaPG.profileCertificate.update({
        data: toUpdateInput,
        where: {
          id: String(filter.id),
        },
      });
    }
    return;
  },
  fetchOneForInternalClient: async (
    filters: Pick<Prisma.ProfileCertificateWhereInput, 'id'>,
  ): Promise<ProfileCertificateForInternalClientI> => {
    const profileCertificateResult = await prismaPG.profileCertificate.findFirst({
      where: filters,
      select: {
        id: true,
        profileId: true,
        fileUrl: true,
        fromDate: true,
        untilDate: true,
        Entity: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        CertificateCourse: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        CertificateCourseRawData: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });
    if (!profileCertificateResult) {
      throw new AppError('PFCRT001');
    }
    const certificateCourse: CertificateCourseNestedClientI = isFilled(profileCertificateResult?.CertificateCourse)
      ? {
          id: profileCertificateResult?.CertificateCourse.id,
          name: profileCertificateResult?.CertificateCourse.name,
          type: profileCertificateResult?.CertificateCourse.type,
          dataType: 'master',
        }
      : {
          id: profileCertificateResult?.CertificateCourseRawData.id,
          name: profileCertificateResult?.CertificateCourseRawData.name,
          type: profileCertificateResult?.CertificateCourseRawData.type,
          dataType: 'raw',
        };

    const entity: EntityNestedClientI = isFilled(profileCertificateResult?.Entity)
      ? {
          id: profileCertificateResult.Entity.id,
          name: profileCertificateResult.Entity.name,
          type: profileCertificateResult.Entity.type,
          dataType: 'master',
        }
      : {
          id: profileCertificateResult.EntityRawData!.id,
          name: profileCertificateResult.EntityRawData!.name,
          type: profileCertificateResult.EntityRawData!.type,
          dataType: 'raw',
        };

    const skills: SkillNestedClientI[] = await SkillModule.fetchForEntityCertificateCourse({
      profileId: profileCertificateResult.profileId,
      certificateCourse,
      entity,
    });
    const profileCertificateForInternalClientResult: ProfileCertificateForInternalClientI = {
      id: profileCertificateResult.id!,
      fromDate: profileCertificateResult.fromDate!,
      untilDate: profileCertificateResult.untilDate!,
      fileUrl: profileCertificateResult.fileUrl!,
      certificateCourse,
      entity,
      skills,
    };
    return profileCertificateForInternalClientResult;
  },
  fetchForClient: async ({
    type,
    page,
    pageSize,
    profileId,
  }: ProfileCertificateFetchForClientI): Promise<ProfileCertificateForExternalClientI[]> => {
    const profileCertificateResult = await prismaPG.profileCertificate.findMany({
      where: {
        profileId: profileId,
        OR: [
          {
            CertificateCourse: {
              type,
            },
          },
          {
            CertificateCourseRawData: {
              type,
            },
          },
        ],
      },
      orderBy: [
        {
          untilDate: 'desc',
        },
        {
          fromDate: 'desc',
        },
        {
          createdAt: 'desc',
        },
      ],
      skip: page * pageSize,
      take: pageSize,
      select: {
        id: true,
        Entity: {
          select: {
            id: true,
            name: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        CertificateCourse: {
          select: {
            id: true,
            name: true,
          },
        },
        CertificateCourseRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        fromDate: true,
        untilDate: true,
        createdAt: true,
      },
    });
    const profileCertificateForClientResult: ProfileCertificateForExternalClientI[] = profileCertificateResult.map(
      (profileCertificate) =>
        ({
          id: profileCertificate.id,
          fromDate: profileCertificate.fromDate,
          untilDate: profileCertificate.untilDate,
          createdAt: profileCertificate.createdAt,
          entity: isFilled(profileCertificate?.Entity)
            ? {
                id: profileCertificate.Entity.id,
                name: profileCertificate.Entity.name,
              }
            : {
                id: profileCertificate.EntityRawData.id,
                name: profileCertificate.EntityRawData.name,
              },
          certificateCourse: isFilled(profileCertificate?.CertificateCourse)
            ? {
                id: profileCertificate.CertificateCourse.id,
                name: profileCertificate.CertificateCourse.name,
              }
            : {
                id: profileCertificate.CertificateCourseRawData.id,
                name: profileCertificate.CertificateCourseRawData.name,
              },
        }) as ProfileCertificateForExternalClientI,
    );
    return profileCertificateForClientResult;
  },
};
