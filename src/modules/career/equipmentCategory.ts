import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type {
  ExperienceEquipmentCategoriesResultI,
  ExperienceEquipmentCategoriesSQLI,
  ExperienceEquipmentCategoryResultI,
  ExperienceEquipmentCategorySQLI,
} from '@interfaces/career/equipmentCategory';
import type { EquipmentCategoryClientI, EquipmentCategoryNestedClientI } from '@interfaces/ship/equipmentCategory';
import type { FuelTypeNestedClientI } from '@interfaces/ship/fuelType';
import { EquipmentCategoriesFetchManyI } from '@schemas/career/equipmentCategory';
import type { RouteParamsI } from '@schemas/common/common';

export const EquipmentCategoryModule = {
  fetchOne: async ({ id }: RouteParamsI): Promise<ExperienceEquipmentCategoryResultI> => {
    const equipmentCategorySQLResult = await prismaPG.$queryRaw<ExperienceEquipmentCategorySQLI[]>`
      WITH
        equipmentCategory
      AS
      (
        SELECT
          x."id",
          x."equipmentCategoryId",
          x."equipmentCategoryRawDataId",
          x."manufacturerName",
          x."model",
          x."powerCapacity",
          x."details",
          e."name" AS "equipmentCategoryName",
          e."hasFuelType" AS "equipmentCategoryHasFuelType",
          erd."name" AS "equipmentCategoryRawDataName",
          erd."hasFuelType" AS "equipmentCategoryRawDataHasFuelType"
        FROM
          "career"."ExperienceEquipmentCategory" x
        LEFT JOIN
          "ship"."EquipmentCategory" e
          ON
          e."id"= x."equipmentCategoryId"
        LEFT JOIN
          "rawData"."EquipmentCategoryRawData" erd
          ON
          erd."id"= x."equipmentCategoryRawDataId"
        WHERE
          x."id" = ${id}::uuid
      )
      SELECT
        ec.*,
        CASE
          WHEN
            ec."equipmentCategoryHasFuelType"
            OR
            ec."equipmentCategoryRawDataHasFuelType"
            THEN (
              SELECT
                json_agg(fuelTypes)
              FROM
              (
                SELECT
                  xf."fuelTypeId",
                  xf."fuelTypeRawDataId",
                  f."name" AS "fuelTypeName",
                  frd."name" AS "fuelTypeRawDataName"
                FROM
                  "career"."ExperienceFuelType" xf
                LEFT JOIN
                  "ship"."FuelType" f
                ON
                  f."id" = xf."fuelTypeId"
                LEFT JOIN
                  "rawData"."FuelTypeRawData" frd
                ON
                  frd."id" = xf."fuelTypeRawDataId"
                WHERE
                  xf."experienceEquipmentCategoryId" = ec."id"
              ) AS fuelTypes
            )
          ELSE NULL
        END AS "fuelTypes"
      FROM equipmentCategory ec
    `;
    if (!equipmentCategorySQLResult?.length) {
      throw new AppError('EXP012');
    }
    const equipmentCategoryResultTemp = equipmentCategorySQLResult[0];
    const equipmentCategoryResult: ExperienceEquipmentCategoryResultI = {
      id: equipmentCategoryResultTemp?.id,
      manufacturerName: equipmentCategoryResultTemp?.manufacturerName,
      model: equipmentCategoryResultTemp?.model,
      details: equipmentCategoryResultTemp?.details,
      powerCapacity: equipmentCategoryResultTemp?.powerCapacity,
      equipmentCategory: equipmentCategoryResultTemp?.equipmentCategoryId
        ? {
            id: equipmentCategoryResultTemp.equipmentCategoryId,
            name: equipmentCategoryResultTemp.equipmentCategoryName,
            hasFuelType: equipmentCategoryResultTemp.equipmentCategoryHasFuelType,
            dataType: 'master',
          }
        : ({
            id: equipmentCategoryResultTemp.equipmentCategoryRawDataId,
            name: equipmentCategoryResultTemp.equipmentCategoryRawDataName,
            hasFuelType: equipmentCategoryResultTemp.equipmentCategoryRawDataHasFuelType,
            dataType: 'raw',
          } as EquipmentCategoryNestedClientI),
      fuelTypes: equipmentCategoryResultTemp?.fuelTypes?.map(
        (fuelTypeItem) =>
          (fuelTypeItem?.fuelTypeId
            ? { id: fuelTypeItem?.fuelTypeId, name: fuelTypeItem?.fuelTypeName, dataType: 'master' }
            : {
                id: fuelTypeItem?.fuelTypeRawDataId,
                name: fuelTypeItem?.fuelTypeRawDataName,
                dataType: 'raw',
              }) as FuelTypeNestedClientI,
      ),
    };
    return equipmentCategoryResult;
  },
  fetchMany: async ({
    experienceShipId,
  }: EquipmentCategoriesFetchManyI): Promise<ExperienceEquipmentCategoriesResultI[]> => {
    const equipmentCategoriesSQLResult = await prismaPG.$queryRaw<ExperienceEquipmentCategoriesSQLI[]>`
      SELECT
        x."id",
        x."equipmentCategoryId",
        x."equipmentCategoryRawDataId",
        e."name" AS "equipmentCategoryName",
        erd."name" AS "equipmentCategoryRawDataName"
      FROM
        "career"."ExperienceEquipmentCategory" x
      LEFT JOIN
        "ship"."EquipmentCategory" e
        ON
        e."id"= x."equipmentCategoryId"
      LEFT JOIN
        "rawData"."EquipmentCategoryRawData" erd
        ON
        erd."id"= x."equipmentCategoryRawDataId"
      WHERE
        x."experienceShipId" = ${experienceShipId}::uuid
    `;
    if (!equipmentCategoriesSQLResult?.length) {
      throw new AppError('EXP012');
    }
    const equipmentCategoriesResult: ExperienceEquipmentCategoriesResultI[] = equipmentCategoriesSQLResult.map(
      (item) => ({
        id: item?.id,
        equipmentCategory: item?.equipmentCategoryId
          ? {
              id: item.equipmentCategoryId,
              name: item.equipmentCategoryName,
              dataType: 'master',
            }
          : ({
              id: item.equipmentCategoryRawDataId,
              name: item.equipmentCategoryRawDataName,
              dataType: 'raw',
            } as EquipmentCategoryClientI),
      }),
    );
    return equipmentCategoriesResult;
  },
};
