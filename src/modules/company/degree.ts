import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameI } from '@consts/master/common';
import { ObjUnknownI, TotalDataI } from '@interfaces/common/data';
import {
  DegreeClientI,
  DegreeModuleFetchsertParamsI,
  DegreeRawQueryFetchForClientResultI,
  DegreeNestedClientI,
} from '@interfaces/company/degree';
import { IdTypeI } from '@schemas/common/common';

export const DegreeModule = {
  fetchById: async (
    filters: IdTypeI,
    select: IdNameI = { id: true, name: true },
    _isThrowingError: boolean = true,
  ): Promise<DegreeClientI> => {
    if (filters.dataType === 'raw') {
      const degreeRawDataResult = await prismaPG.degreeRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(degreeRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as DegreeClientI;
    } else if (filters.dataType === 'master') {
      const degreeResult = await prismaPG.degree.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(degreeResult as ObjUnknownI),
        dataType: 'master',
      } as DegreeClientI;
    }

    if (_isThrowingError) {
      throw new AppError('ORG001');
    }
  },
  fetchForClient: async (name?: string, { page, pageSize } = PAGINATION): Promise<TotalDataI<DegreeNestedClientI>> => {
    name = name?.trim()?.toLowerCase();
    const [degreesResultTemp, degreesTotalResult] = await Promise.all([
      prismaPG.$queryRaw<DegreeNestedClientI[]>`
     SELECT *
          FROM
          (
            (SELECT
            d."id",
            d."name",
            'master' AS "dataType"
            FROM
            "company"."Degree" d
            WHERE
            d."name" ILIKE ${name + '%'})
            UNION ALL
            (SELECT
            drw."id",
            drw."name",
            'raw' AS "dataType"
            FROM
            "rawData"."DegreeRawData" drw
            WHERE
            drw."name" ILIKE ${name + '%'})
          )
          ORDER BY "name" ASC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
      `,
      prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_degree AS (
                SELECT 1
                FROM "company"."Degree" degree
                WHERE
                  degree."name" ILIKE ${name + '%'}
              ),
              raw_degree AS (
                SELECT 1
                FROM "rawData"."DegreeRawData" degreeRawData
                WHERE
                  degreeRawData."name" ILIKE ${name + '%'}
              ),
              combined AS (
                SELECT * FROM master_degree
                UNION ALL
                SELECT * FROM raw_degree
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `,
    ]);

    return {
      data: degreesResultTemp,
      total: Number(degreesTotalResult[0]?.total || 0),
    };
  },

  fetchsert: async (params: DegreeModuleFetchsertParamsI): Promise<DegreeClientI> => {
    const cleanName = params.name.trim();
    const degreeResult = await prismaPG.$queryRaw<DegreeRawQueryFetchForClientResultI[]>`
    SELECT * FROM (
      (SELECT
        d."id",
        d."name",
        'master' AS "dataType"
      FROM "company"."Degree" d
      WHERE d."name" ILIKE ${cleanName}
      LIMIT 1)

      UNION ALL

      (SELECT
        drw."id",
        drw."name",
        'raw' AS "dataType"
      FROM "rawData"."DegreeRawData" drw
      WHERE drw."name" ILIKE ${cleanName}
      LIMIT 1)
    ) combined_degrees
    ORDER BY "dataType" ASC
    LIMIT 1
  `;

    if (degreeResult.length) {
      const { id, name, dataType } = degreeResult[0];
      return { id, name, dataType: dataType as DBDataTypeI } as DegreeClientI;
    }

    const inserted = await prismaPG.$transaction(async (tx: typeof prismaPG) => {
      const existing = await tx.degreeRawData.findFirst({
        where: {
          name: { mode: 'insensitive', equals: cleanName },
        },
      });
      if (existing) return existing;

      return tx.degreeRawData.create({
        data: {
          name: cleanName,
          dataStatus: 'PENDING',
        },
      });
    });

    if (!inserted?.id) {
      throw new AppError('DGR002');
    }

    return {
      ...inserted,
      dataType: 'raw' as DBDataTypeI,
    } as DegreeClientI;
  },
  count: async (masterDegrees: string[], rawDataDegrees: string[]) => {
    masterDegrees = Array.from(new Set<string>(masterDegrees));
    rawDataDegrees = Array.from(new Set<string>(rawDataDegrees));
    const [countMasterDegrees, countRawDataDegrees] = await Promise.all([
      masterDegrees?.length
        ? prismaPG.degree.count({
            where: {
              id: {
                in: masterDegrees,
              },
            },
          })
        : 0,
      rawDataDegrees?.length
        ? prismaPG.degreeRawData.count({
            where: {
              id: {
                in: rawDataDegrees,
              },
            },
          })
        : 0,
    ]);
    return { countMasterDegrees, countRawDataDegrees };
  },
};
