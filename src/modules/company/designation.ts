import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameI } from '@consts/master/common';
import { ObjUnknownI, TotalDataI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  DesignationClientI,
  DesignationModuleFetchsertParamsI,
  DesignationNestedClientI,
} from '@interfaces/company/designation';
import { IdTypeI } from '@schemas/common/common';
export const DesignationModule = {
  fetchById: async (filters: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<DesignationClientI> => {
    const select: IdNameI = { id: true, name: true };
    if (filters.dataType === 'raw') {
      const designationRawDataResult = await txn.designationRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(designationRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as DesignationClientI;
    } else if (filters.dataType === 'master') {
      const designationAlternativeResult = await txn.designationAlternative.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(designationAlternativeResult as ObjUnknownI),
        dataType: 'master',
      } as DesignationClientI;
    }
    throw new AppError('DSG001');
  },
  fetchForClient: async (
    name?: string,
    { page, pageSize } = PAGINATION,
  ): Promise<TotalDataI<DesignationNestedClientI>> => {
    name = name?.trim()?.toLowerCase();

    const [designationsResultTemp, designationsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<DesignationNestedClientI[]>`
          SELECT *
          FROM
          (
            (SELECT
            da."id",
            da."name",
            'master' AS "dataType"
            FROM
            "company"."DesignationAlternative" da
            WHERE
            da."name" ILIKE ${name + '%'})
            UNION ALL
            (SELECT
            drw."id",
            drw."name",
            'raw' AS "dataType"
            FROM
            "rawData"."DesignationRawData" drw
            WHERE
            drw."name" ILIKE ${name + '%'})
          )
          ORDER BY "name" ASC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
      `,
      prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_designation AS (
                SELECT 1
                FROM "company"."DesignationAlternative" designation
                WHERE
                  designation."name" ILIKE ${name + '%'}
              ),
              raw_designation AS (
                SELECT 1
                FROM "rawData"."DesignationRawData" designationRawData
                WHERE
                  designationRawData."name" ILIKE ${name + '%'}
              ),
              combined AS (
                SELECT * FROM master_designation
                UNION ALL
                SELECT * FROM raw_designation
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `,
    ]);

    return {
      data: designationsResultTemp,
      total: Number(designationsTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async (params: DesignationModuleFetchsertParamsI): Promise<DesignationClientI> => {
    const name = params.name.trim().toLowerCase();
    const existingDesignationResult = await prismaPG.$queryRaw<DesignationClientI[]>`
      SELECT * FROM (
        (SELECT
          da."id",
          da."name",
          'master' AS "dataType"
        FROM "company"."DesignationAlternative" da
        WHERE da."name" ILIKE ${name})

        UNION ALL

        (SELECT
          drw."id",
          drw."name",
          'raw' AS "dataType"
        FROM "rawData"."DesignationRawData" drw
        WHERE drw."name" ILIKE ${name})
      ) combined
      LIMIT 1
    `;

    const existingDesignation = existingDesignationResult[0];

    if (existingDesignation) {
      return existingDesignation;
    }

    const result = await prismaPG.designationRawData.create({ data: params });
    if (!result?.id) {
      throw new AppError('DSG002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as DesignationClientI;
  },
  count: async (masterDesignations: string[], rawDataDesignations: string[]) => {
    masterDesignations = Array.from(new Set<string>(masterDesignations));
    rawDataDesignations = Array.from(new Set<string>(rawDataDesignations));
    const [countMasterDesignations, countRawDataDesignations] = await Promise.all([
      masterDesignations?.length
        ? prismaPG.designationAlternative.count({
            where: {
              id: {
                in: masterDesignations,
              },
            },
          })
        : 0,
      rawDataDesignations?.length
        ? prismaPG.designationRawData.count({
            where: {
              id: {
                in: rawDataDesignations,
              },
            },
          })
        : 0,
    ]);
    return { countMasterDesignations, countRawDataDesignations };
  },
};
