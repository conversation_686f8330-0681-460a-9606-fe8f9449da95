import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PrismaErrorCodes } from '@consts/common/error/prismaErrorCodes';
import { Prisma, ProfileStatus } from '@prisma/postgres';
const ProfileStatusModule = {
  updateOne: async (
    params: Prisma.ProfileStatusUncheckedUpdateInput,
    filters: Pick<Prisma.ProfileStatusWhereUniqueInput, 'profileId'>,
    select: Prisma.ProfileStatusSelect = {
      profileId: true,
      isPasswordSaved: true,
      isEmailVerified: true,
      isMobileVerified: true,
      isPersonalDetailsSaved: true,
      isWorkDetailsSaved: true,
      isPrivacyPolicyAccepted: true,
    },
  ): Promise<ProfileStatus> => {
    try {
      const result: ProfileStatus = await prismaPG.profileStatus.update({
        data: params as Prisma.ProfileStatusUpdateInput,
        where: filters,
        select,
      });
      if (!result?.profileId) {
        throw new AppError('PFSTS003');
      }
      return result;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === PrismaErrorCodes.RECORD_NOT_FOUND) {
          throw new AppError('PFSTS003');
        }
      }
      throw error;
    }
  },
  fetchById: async (
    filters: Pick<Prisma.ProfileStatusWhereInput, 'profileId'>,
    select: Prisma.ProfileStatusSelect = {
      profileId: true,
      isPasswordSaved: true,
      isEmailVerified: true,
      isMobileVerified: true,
      isPersonalDetailsSaved: true,
      isWorkDetailsSaved: true,
    },
  ): Promise<ProfileStatus> => {
    const profileStatusResult = await prismaPG.profileStatus.findFirst({
      where: filters,
      select,
    });
    if (!profileStatusResult) {
      throw new AppError('PFSTS001');
    }
    return profileStatusResult;
  },
};
export default ProfileStatusModule;
