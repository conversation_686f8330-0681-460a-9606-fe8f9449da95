import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FetchCommunityMemberI } from '@interfaces/forum/member';
import { Prisma, MemberTypeE, CommunityMember } from '@prisma/postgres';
import { CommunityMemberFetchForClientI } from '@schemas/forum/member';
import { errorHandler } from '@utils/errors/handler';

const CommunityMemberModule = {
  fetchMember: async (
    { communityId, profileId }: CommunityMemberFetchForClientI,
    throwsError: boolean = true,
  ): Promise<Pick<CommunityMember, 'type'> | null> => {
    const memberResult = await prismaPG.communityMember.findUnique({
      select: {
        type: true,
      },
      where: {
        communityId_profileId: {
          communityId,
          profileId,
        },
      },
    });
    if (throwsError && !memberResult) {
      throw new AppError('FMMB007');
    }
    return memberResult;
  },
  fetchMemberForClient: async ({
    profileId,
    communityId,
  }: CommunityMemberFetchForClientI): Promise<FetchCommunityMemberI> => {
    const result = await prismaPG.$queryRaw<FetchCommunityMemberI>`
      SELECT
        cm."communityId",
        cm."profileId",
        cm."type",
        p."username",
        p."name" AS "profileName",
        p."avatar",
        c."name" AS "communityName"
      FROM "forum"."CommunityMember" cm
      JOIN "user"."Profile" p ON cm."profileId" = p."id"
      JOIN "forum"."Community" c ON cm."communityId" = c."id"
      WHERE
        cm."profileId" = ${profileId}::uuid
        ${communityId ? Prisma.sql`AND cm."communityId" = ${communityId}::uuid` : Prisma.empty}
    `;

    if (!result) {
      throw new AppError('FMMB001');
    }

    return {
      type: result[0].type,
      profile: {
        id: result[0].profileId,
        username: result[0].username,
        name: result[0].profileName,
        avatar: result[0].avatar,
      },
      community: {
        id: result[0].communityId,
        name: result[0].communityName,
      },
    };
  },
  leaveCommunity: async ({ profileId, communityId }: CommunityMemberFetchForClientI): Promise<void> => {
    try {
      const member = await prismaPG.communityMember.findFirst({
        where: { profileId, communityId },
        select: {
          type: true,
        },
      });
      if (!member) {
        throw new AppError('FMMB001');
      }

      if (member.type === MemberTypeE.ADMIN) {
        const adminCount = await prismaPG.communityMember.count({
          where: {
            communityId,
            type: MemberTypeE.ADMIN,
          },
        });

        if (adminCount <= 1) {
          throw new AppError('FMMB003');
        }
      }
      await prismaPG.communityMember.delete({
        where: {
          communityId_profileId: {
            communityId,
            profileId,
          },
        },
      });
      await prismaPG.community.update({
        where: { id: communityId },
        data: {
          memberCount: {
            decrement: 1,
          },
        },
      });
    } catch (error) {
      errorHandler(error);
    }
  },
};

export default CommunityMemberModule;
