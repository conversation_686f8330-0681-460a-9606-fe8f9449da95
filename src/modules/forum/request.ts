import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { CommunityRequestBodyI, CommunityRequestRevocationQueryI } from '@schemas/forum/request';
import { CommunityRequestStatusE, Prisma } from '@prisma/postgres';
import { MemberTypeI } from '@consts/forum/member';
import { CommunityRequestCreateOneResultI } from '@interfaces/forum/request';

const CommunityRequestModule = {
  createOne: async ({
    communityId,
    profileId,
    requestedType,
  }: CommunityRequestBodyI): Promise<CommunityRequestCreateOneResultI> => {
    const existingMemberResult = await prismaPG.communityMember.findFirst({
      select: { communityId: true, profileId: true },
      where: {
        communityId,
        profileId,
      },
    });
    if (existingMemberResult) {
      throw new AppError('CMRQ003');
    }
    const requestSelect: Prisma.CommunityRequestSelect = { status: true };
    const existingRequestResult = await prismaPG.communityRequest.findFirst({
      select: requestSelect,
      where: {
        communityId,
        profileId,
      },
    });
    if (existingRequestResult.status === 'PENDING') {
      throw new AppError('CMRQ002');
    }
    const communityResult = await prismaPG.community.findUnique({
      where: { id: communityId },
      select: {
        access: true,
        isRestricted: true,
      },
    });
    if (!communityResult) {
      throw new AppError('FMMB004');
    }
    const toCreateRequest: Prisma.CommunityRequestUncheckedCreateInput = { communityId, profileId, requestedType };

    if ((communityResult.access === 'PUBLIC' && communityResult.isRestricted) || communityResult.access === 'PRIVATE') {
      switch (requestedType as MemberTypeI) {
        case 'ADMIN':
        case 'MODERATOR':
        case 'CONTRIBUTOR':
        case 'MEMBER': {
          toCreateRequest.status = 'PENDING';
          break;
        }
        default: {
          throw new AppError('FMMB005');
        }
      }
    } else if (communityResult.access === 'PUBLIC') {
      switch (requestedType as MemberTypeI) {
        case 'ADMIN':
        case 'MODERATOR': {
          toCreateRequest.status = 'PENDING';
          break;
        }
        case 'CONTRIBUTOR':
        case 'MEMBER': {
          toCreateRequest.status = 'ACCEPTED';
          toCreateRequest.acceptedType = 'CONTRIBUTOR';
          break;
        }
        default: {
          throw new AppError('FMMB005');
        }
      }
    }
    const requestResult = (await existingRequestResult?.status)
      ? prismaPG.communityRequest.update({
          data: toCreateRequest,
          select: requestSelect,
          where: {
            communityId_profileId: {
              communityId,
              profileId,
            },
          },
        })
      : prismaPG.communityRequest.create({
          data: toCreateRequest,
          select: requestSelect,
        });
    return requestResult;
  },
  // approveRequestTypeChange: async (
  //   state: FastifyStateI,
  //   { profileId, communityId }: CommunitRequestApprovalQueryI,
  //   { acceptedType }: CommunityRequestApprovalBodyI,
  // ): Promise<FetchCommunityRequestI> => {
  //   const adminProfileId = state.profileId || 'c6f84df2-b673-489e-9092-68ea2b52704c';
  //   const communityMemberResult = await prismaPG.communityMember.findFirst({
  //     where: {
  //       profileId: adminProfileId,
  //       communityId,
  //     },
  //   });

  //   if (!communityMemberResult || communityMemberResult.type !== 'ADMIN') {
  //     throw new AppError('CMTY004');
  //   }

  //   const request = await prismaPG.communityRequest.findFirst({
  //     where: {
  //       profileId,
  //       communityId,
  //       status: CommunityRequestStatusE.PENDING,
  //     },
  //   });
  //   if (!request) {
  //     throw new AppError('CMRQ004');
  //   }
  //   const status =
  //     request.requestedType === acceptedType
  //       ? CommunityRequestStatusE.ACCEPTED
  //       : CommunityRequestStatusE.PARTIALLY_ACCEPTED;

  //   const result = await prismaPG.$transaction(async (tx) => {
  //     const communityRequestResult = await tx.communityRequest.update({
  //       where: {
  //         communityId_profileId: {
  //           communityId,
  //           profileId,
  //         },
  //       },
  //       data: {
  //         acceptedType,
  //         status,
  //       },
  //     });

  //     if ([CommunityRequestStatusE.ACCEPTED, CommunityRequestStatusE.PARTIALLY_ACCEPTED].includes(status)) {
  //       await prismaPG.$transaction([
  //         tx.communityMember.create({
  //           data: { communityId, profileId, type: acceptedType },
  //         }),
  //         tx.community.update({
  //           where: { id: communityId },
  //           data: { membersCount: { increment: 1 } },
  //         }),
  //       ]);
  //     }
  //     return communityRequestResult;
  //   });

  //   return result;
  // },
  // rejectRequest: async (
  //   state: FastifyStateI,
  //   { profileId, communityId }: CommunityRequestRejectionQueryI,
  // ): Promise<void> => {
  //   const adminProfileId = state.profileId || 'c6f84df2-b673-489e-9092-68ea2b52704c';
  //   try {
  //     const adminMember = await prismaPG.communityMember.findFirst({
  //       where: {
  //         profileId: adminProfileId,
  //         communityId,
  //         type: MemberTypeE.ADMIN,
  //       },
  //     });

  //     if (!adminMember) {
  //       throw new AppError('CMTY004');
  //     }

  //     const request = await prismaPG.communityRequest.findFirst({
  //       where: {
  //         profileId,
  //         communityId,
  //         status: CommunityRequestStatusE.PENDING,
  //       },
  //     });

  //     if (!request) {
  //       throw new AppError('CMRQ004');
  //     }

  //     await prismaPG.communityRequest.delete({
  //       where: {
  //         communityId_profileId: {
  //           communityId,
  //           profileId,
  //         },
  //       },
  //     });
  //   } catch (error) {
  //     errorHandler(error);
  //   }
  // },

  revokeRequest: async ({ profileId, communityId }: CommunityRequestRevocationQueryI): Promise<void> => {
    const request = await prismaPG.communityRequest.findFirst({
      where: {
        profileId,
        communityId,
        status: CommunityRequestStatusE.PENDING,
      },
    });

    if (!request) {
      throw new AppError('CMRQ004');
    }

    await prismaPG.communityRequest.update({
      where: {
        communityId_profileId: {
          communityId,
          profileId,
        },
      },
      data: {
        status: CommunityRequestStatusE.REVOKED,
      },
    });
  },
};

export default CommunityRequestModule;
