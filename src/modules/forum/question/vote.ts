import { FastifyStateI } from '@interfaces/common/declaration';
import type { Prisma } from '@prisma/postgres';
import { QuestionModule } from './question';
import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import type {
  ForumQuestionVoteCreateOneI,
  ForumQuestionVoteDeleteOneI,
  ForumQuestionVoteFetchManyI,
} from '@schemas/forum/vote';
import type { ProfileExternalI } from '@interfaces/user/profile';
import type { IdCursorIdI, TotalCursorDataI } from '@interfaces/common/data';
import User from '@modules/user';
import { CommunityModule } from '../community/community';

export const QuestionVoteModule = {
  fetchOne: async (
    filters: Prisma.QuestionVoteWhereUniqueInput,
    select: Prisma.QuestionVoteSelect = {
      id: true,
    },
    isThrowingError: boolean = true,
  ) => {
    const voteResult = await prismaPG.questionVote.findUnique({
      select,
      where: filters,
    });
    if (isThrowingError && !voteResult) {
      throw new AppError('FMQVT002');
    }
    return voteResult;
  },
  createOne: async (
    state: FastifyStateI,
    { questionId, type: requestedType }: ForumQuestionVoteCreateOneI,
  ): Promise<IdCursorIdI> => {
    const selfProfileId = state.profileId;
    const questionResult = await QuestionModule.fetchById({ id: questionId }, { communityId: true });
    const communityId = questionResult.communityId;

    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    const existingVoteResult = await QuestionVoteModule.fetchOne(
      {
        questionId_profileId: { questionId, profileId: selfProfileId },
      },
      {
        id: true,
        type: true,
      },
      false,
    );
    if (existingVoteResult?.type === requestedType) {
      switch (requestedType) {
        case 'UPVOTE': {
          throw new AppError('FMAVT003');
        }
        case 'DOWNVOTE': {
          throw new AppError('FMAVT004');
        }
      }
    }
    const [voteResult, _questionResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          existingVoteResult
            ? txn.questionVote.update({
                data: {
                  type: requestedType,
                },
                select: { id: true, cursorId: true },
                where: { id: existingVoteResult.id },
              })
            : txn.questionVote.create({
                data: {
                  questionId,
                  communityId,
                  profileId: selfProfileId,
                  type: requestedType,
                },
                select: { id: true, cursorId: true },
              }),
          txn.question.update({
            data:
              requestedType === 'UPVOTE'
                ? {
                    upvoteCount: {
                      increment: 1,
                    },
                  }
                : {
                    downvoteCount: {
                      increment: 1,
                    },
                  },
            where: {
              id: questionId,
            },
            select: { id: true },
          }),
        ]),
    );
    if (!voteResult) {
      throw new AppError('FMQVT001');
    }
    return { id: voteResult.id, cursorId: Number(voteResult.cursorId) };
  },
  deleteOne: async (state: FastifyStateI, { questionId }: ForumQuestionVoteDeleteOneI): Promise<void> => {
    const selfProfileId = state.profileId;
    const existingVoteResult = await QuestionVoteModule.fetchOne(
      {
        questionId_profileId: {
          questionId,
          profileId: selfProfileId,
        },
      },
      {
        id: true,
        questionId: true,
        communityId: true,
        type: true,
        Question: {
          select: {
            upvoteCount: true,
            downvoteCount: true,
          },
        },
      },
    );
    const communityId = existingVoteResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const [deletedVoteResult, _updatedQuestionResult] = await prismaPG.$transaction(async (txn) => {
      const [deletedVoteResult, updatedQuestionResult] = await Promise.all([
        txn.questionVote.delete({
          select: { id: true },
          where: { id: existingVoteResult.id },
        }),
        existingVoteResult.type === 'UPVOTE' && existingVoteResult?.Question?.upvoteCount > 0
          ? txn.question.update({
              data: {
                upvoteCount: {
                  decrement: 1,
                },
              },
              select: { id: true },
              where: {
                id: existingVoteResult.questionId,
              },
            })
          : existingVoteResult.type === 'DOWNVOTE' && existingVoteResult?.Question?.downvoteCount > 0
            ? txn.question.update({
                data: {
                  downvoteCount: {
                    decrement: 1,
                  },
                },
                select: { id: true },
                where: {
                  id: existingVoteResult.questionId,
                },
              })
            : null,
      ]);
      return [deletedVoteResult, updatedQuestionResult];
    });
    if (!deletedVoteResult) {
      throw new AppError('FMAVT005');
    }
    return;
  },
  fetchMany: async (
    state: FastifyStateI,
    { questionId, cursorId, pageSize, type }: ForumQuestionVoteFetchManyI,
  ): Promise<TotalCursorDataI<ProfileExternalI>> => {
    const selfProfileId = state.profileId;
    const filters: Prisma.QuestionVoteWhereInput = {
      questionId,
      type,
      Profile: {
        status: 'ACTIVE',
      },
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };
    const [total, votedProfileResultTemp] = await Promise.all([
      prismaPG.questionVote.count({
        where: filters,
      }),
      prismaPG.questionVote.findMany({
        select: {
          id: true,
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
        },
        where: {
          ...filters,
          ...(typeof cursorId === 'number' && cursorId > 0
            ? {
                cursorId: {
                  lt: BigInt(cursorId),
                },
              }
            : {}),
        },
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    const votedProfileResult: ProfileExternalI[] = [];
    let nextCursorId = null;
    if (votedProfileResultTemp?.length) {
      votedProfileResult.push(
        ...votedProfileResultTemp.map(
          (votedProfile) => User.ProfileModule.transformProfile(votedProfile.Profile) as ProfileExternalI,
        ),
      );
      nextCursorId = votedProfileResult[votedProfileResult.length - 1].cursorId;
    }
    return { total, data: votedProfileResult, nextCursorId };
  },
};
