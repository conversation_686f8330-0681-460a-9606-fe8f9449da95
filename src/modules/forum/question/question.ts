import { prismaPG } from '@config/db';
import type {
  ForumQuestionDetailWithAnswersI,
  ForumQuestionDetailWithAnswersSQLI,
  ForumQuestionFetchManyResultI,
  ForumQuestionFetchManySQLI,
  ForumQuestionSearchItemI,
  ForumQuestionSearchResultI,
} from '@interfaces/forum/question';
import type {
  GlobalSearchQuestionItemI,
  GlobalSearchResponseI,
} from '@interfaces/forum/search';
import type {
  ForumQuestionUpdateLiveI,
  ForumQuestionCreateOneI,
  ForumQuestionDeleteOneI,
  ForumQuestionFetchManyI,
  ForumQuestionSearchI,
  GlobalSearchParamsI,
} from '@schemas/forum/question';
import { MemberTypeE, Prisma, Question } from '@prisma/postgres';
import type { FastifyStateI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import { errorHandler } from '@utils/errors/handler';
import { CommunityModule } from '../community/community';
import CommunityMemberModule from '../member';
import { DepartmentModule } from '@modules/company/department';
import { EquipmentCategoryModule } from '@modules/ship/equipmentCategory';
import { EquipmentModelModule } from '@modules/ship/equipmentModel';
import { EquipmentManufacturerModule } from '@modules/ship/equipmentManufacturer';
import { TopicModule } from '../topic';
import { isNullUndefined } from '@utils/data/data';
import type { DateNullI, NumberNullI, TotalCursorDateDataI } from '@interfaces/common/data';
import Ship from '@modules/ship';
import type { RouteParamsI } from '@schemas/common/common';
import { addMsToDate, getCurrentDate, subMsToDate } from '@utils/data/date';
import AppConfig from '@modules/appConfig';
import type { ForumQuestionConfigI } from '@interfaces/appConfig/appConfig';
import type { TotalI } from '@interfaces/common/db';

export const QuestionModule = {
  fetchById: async (
    { id }: Pick<Prisma.QuestionWhereUniqueInput, 'id'>,
    select: Prisma.QuestionSelect = {
      id: true,
    },
  ) => {
    const questionResult = await prismaPG.question.findUnique({
      select,
      where: {
        id,
      },
    });
    if (!questionResult) {
      throw new AppError('FMQUE008');
    }
    return questionResult;
  },
  createOne: async (
    state: FastifyStateI,
    {
      communityId,
      department,
      description,
      equipmentCategory,
      equipmentManufacturer,
      equipmentModel,
      files,
      isAnonymous,
      isLive,
      ship,
      title,
      topics,
      type,
    }: ForumQuestionCreateOneI,
  ): Promise<Pick<Question, 'id'>> => {
    try {
      const selfProfileId = state.profileId;
      const communityResult = await CommunityModule.fetchById({
        id: communityId,
      });
      if (!communityResult) {
        throw new AppError('CMTY007');
      }
      if (
        !(
          communityResult?.access === 'GLOBAL' ||
          (communityResult?.access === 'PUBLIC' && !communityResult?.isRestricted)
        )
      ) {
        await CommunityMemberModule.fetchMember({
          communityId,
        });
      }
      const questionInput: Prisma.QuestionUncheckedCreateInput = {
        title: title,
        description: description,
        type: type,
        communityId: communityId,
        profileId: selfProfileId,
        isLive,
      };
      if (isLive) {
        questionInput.liveStartedAt = getCurrentDate();
      }
      const [_departmentResult, shipResult] = await Promise.all([
        department ? DepartmentModule.fetchById(department, { id: true }) : null,
        ship
          ? Ship.CoreShipModule.fetchByImoSelected(ship, null, { subVesselTypeId: true, mainVesselTypeId: true })
          : null,
      ]);
      switch (department?.dataType) {
        case 'master': {
          questionInput.departmentAlternativeId = department.id;
          break;
        }
        case 'raw': {
          questionInput.departmentRawDataId = department.id;
          break;
        }
      }
      if (shipResult) {
        if (shipResult.dataType === 'master') {
          questionInput.shipImo = ship.imo;
          questionInput.mainVesselTypeId = shipResult.mainVesselTypeId;
          questionInput.subVesselTypeId = shipResult.imo;
        } else if (shipResult.dataType === 'raw') {
          questionInput.shipRawDataImo = shipResult.imo;

          if (shipResult.mainVesselTypeId) {
            questionInput.mainVesselTypeId = shipResult.mainVesselTypeId;
          } else if (shipResult.mainVesselTypeRawDataId) {
            questionInput.mainVesselTypeRawDataId = shipResult.mainVesselTypeRawDataId;
          }

          if (shipResult.subVesselTypeId) {
            questionInput.subVesselTypeId = shipResult.subVesselTypeId;
          } else if (shipResult.subVesselTypeRawDataId) {
            questionInput.subVesselTypeRawDataId = shipResult.subVesselTypeRawDataId;
          }
        }
      }

      if (!isNullUndefined(isAnonymous)) {
        questionInput.isAnonymous = isAnonymous;
      }
      if (files?.length) {
        questionInput.QuestionMedia = {
          createMany: {
            data: files.map((file) => ({ communityId, fileUrl: file.fileUrl, fileExtension: file.fileExtension })),
          },
        };
      }

      if (type === 'NORMAL') {
        await Promise.all(topics.map((topic) => TopicModule.fetchById(topic)));
        questionInput.QuestionTopic = {
          createMany: {
            data: topics.map((topic) =>
              topic.dataType === 'master'
                ? {
                    topicId: topic.id,
                    communityId: communityId,
                  }
                : {
                    topicRawDataId: topic.id,
                    communityId: communityId,
                  },
            ),
          },
        };
      } else {
        await Promise.all([
          EquipmentCategoryModule.fetchById(equipmentCategory),
          EquipmentManufacturerModule.fetchById(equipmentManufacturer),
          EquipmentModelModule.fetchById(equipmentModel),
        ]);
        if (equipmentCategory.dataType === 'master') {
          questionInput.equipmentCategoryId = equipmentCategory.id;
        } else {
          questionInput.equipmentCategoryRawDataId = equipmentCategory.id;
        }
        if (equipmentManufacturer.dataType === 'master') {
          questionInput.equipmentManufacturerId = equipmentManufacturer.id;
        } else {
          questionInput.equipmentManufacturerRawDataId = equipmentManufacturer.id;
        }
        if (equipmentModel.dataType === 'master') {
          questionInput.equipmentModelId = equipmentModel.id;
        } else {
          questionInput.equipmentModelRawDataId = equipmentModel.id;
        }
      }
      const [questionResult, _updatedCommunity] = await prismaPG.$transaction(
        async (txn) =>
          await Promise.all([
            txn.question.create({
              data: questionInput,
              select: {
                id: true,
                title: true,
                description: true,
                type: true,
              },
            }),
            txn.community.update({
              data: { questionCount: { increment: 1 } },
              where: {
                id: communityId,
              },
            }),
          ]),
      );
      if (!questionResult) {
        throw new AppError('FMQUE003');
      }
      return { id: questionResult.id };
    } catch (error) {
      errorHandler(error);
    }
  },
  deleteOne: async (state: FastifyStateI, { id: questionId }: ForumQuestionDeleteOneI) => {
    const selfProfileId = state.profileId;

    const question = await prismaPG.question.findUnique({
      where: {
        id: questionId,
      },
      select: {
        profileId: true,
        communityId: true,
      },
    });

    if (!question) {
      throw new AppError('DB002');
    }

    const communityId = question.communityId;
    const isAuthor = question.profileId === selfProfileId;
    if (!isAuthor) {
      const communityMember = await prismaPG.communityMember.findUnique({
        where: {
          communityId_profileId: {
            communityId,
            profileId: selfProfileId,
          },
        },
        select: {
          type: true,
        },
      });
      if (
        !communityMember ||
        (communityMember.type !== MemberTypeE.ADMIN && communityMember.type !== MemberTypeE.MODERATOR)
      ) {
        throw new AppError('FMQUE006');
      }
    }

    return await prismaPG.$transaction(async (tx) => {
      const questionTopics = await tx.questionTopic.findMany({
        where: { questionId },
        select: {
          topicId: true,
          topicRawDataId: true,
        },
      });
      await tx.answer.deleteMany({ where: { questionId } });
      await tx.answerVote.deleteMany({
        where: {
          Answer: {
            questionId,
          },
        },
      });
      await tx.questionVote.deleteMany({ where: { questionId } });
      await tx.questionMedia.deleteMany({ where: { questionId } });
      await tx.questionTopic.deleteMany({ where: { questionId } });
      const deletedQuestion = await tx.question.delete({ where: { id: questionId } });

      await Promise.all([
        ...questionTopics
          .filter((qt) => qt.topicId)
          .map((qt) =>
            tx.topic.update({
              where: { id: qt.topicId! },
              data: { count: { decrement: 1 } },
            }),
          ),

        ...questionTopics
          .filter((qt) => qt.topicRawDataId)
          .map((qt) =>
            tx.topicRawData.update({
              where: { id: qt.topicRawDataId! },
              data: { count: { decrement: 1 } },
            }),
          ),
      ]);

      return deletedQuestion;
    });
  },
  fetchOneForClient: async (
    state: FastifyStateI,
    { id: questionId }: RouteParamsI,
  ): Promise<ForumQuestionDetailWithAnswersI> => {
    const selfProfileId = state.profileId;
    const questionResult = await QuestionModule.fetchById({ id: questionId }, { communityId: true, isAnonymous: true });
    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const questionTempArr: ForumQuestionDetailWithAnswersSQLI[] = await prismaPG.$queryRaw`
  WITH question_data AS (
    SELECT
      q."id",
      q."title",
      q."description",
      q."type",
      q."upvoteCount",
      q."downvoteCount",
      q."answerCount",
      q."commentCount",
      q."isLive",
      q."liveStartedAt",
      q."isSolved",
      q."isEdited",
      (
        SELECT v."type" FROM "forum"."QuestionVote" v
        WHERE v."questionId" = q."id"
        AND v."profileId" = ${selfProfileId}::uuid
        LIMIT 1
      ) AS "vote",
      (
        SELECT json_agg(
          json_build_object(
            'id', m."id",
            'fileUrl', m."fileUrl",
            'fileExtension', m."fileExtension"
          )
        )
        FROM "forum"."QuestionMedia" m
        WHERE m."questionId" = q."id"
      ) AS "media",
      q."equipmentCategoryId",
      ec."name" AS "equipmentCategoryName",
      q."equipmentCategoryRawDataId",
      ecr."name" AS "equipmentCategoryRawDataName",
      q."equipmentModelId",
      em."name" AS "equipmentModelName",
      q."equipmentModelRawDataId",
      emr."name" AS "equipmentModelRawDataName",
      q."equipmentManufacturerId",
      emf."name" AS "equipmentManufacturerName",
      q."equipmentManufacturerRawDataId",
      emfr."name" AS "equipmentManufacturerRawDataName",
      q."createdAt",
      p."id" AS "profileId"
      ${
        !questionResult.isAnonymous
          ? Prisma.sql`, p."name" AS "profileName", p."avatar" AS "profileAvatar"`
          : Prisma.empty
      },
      (
        SELECT json_agg(
          CASE
            WHEN qt."topicId" IS NOT NULL THEN
              json_build_object(
                'id', t.id,
                'name', t.name,
                'dataType', 'master'
              )
            ELSE
              json_build_object(
                'id', tr.id,
                'name', tr.name,
                'dataType', 'raw'
              )
          END
        )
        FROM "forum"."QuestionTopic" qt
        LEFT JOIN "forum"."Topic" t ON qt."topicId" = t.id
        LEFT JOIN "rawData"."TopicRawData" tr ON qt."topicRawDataId" = tr.id
        WHERE qt."questionId" = q.id
      ) AS topics
    FROM "forum"."Question" q
    INNER JOIN "user"."Profile" p
      ON q."profileId" = p."id"
      AND p."status" = 'ACTIVE'
    LEFT JOIN "network"."BlockedProfile" b1
      ON b1."blockerId" = ${selfProfileId}::uuid
      AND b1."blockedId" = p."id"
    LEFT JOIN "network"."BlockedProfile" b2
      ON b2."blockerId" = p."id"
      AND b2."blockedId" = ${selfProfileId}::uuid
    LEFT JOIN "ship"."EquipmentCategory" ec
      ON ec."id" = q."equipmentCategoryId"
    LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr
      ON ecr."id" = q."equipmentCategoryRawDataId"
    LEFT JOIN "ship"."EquipmentModel" em
      ON em."id" = q."equipmentModelId"
    LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
    LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
    LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
    WHERE q.id = ${questionId}::uuid
      AND b1."blockerId" IS NULL
      AND b2."blockerId" IS NULL
  )
  SELECT
    qd.*,
    (
      SELECT json_agg(
        json_build_object(
          'id', a.id,
          'text', a.text,
          'upvoteCount', a."upvoteCount",
          'downvoteCount', a."downvoteCount",
          'commentCount', a."commentCount",
          'status', a.status,
          'isEdited', a."isEdited",
          'createdAt', a."createdAt",
          'profile', json_build_object(
            'id', ap.id,
            'name', ap.name,
            'avatar', ap."avatar"
          ),
          'media', (
            SELECT json_agg(
              json_build_object(
                'id', m."id",
                'fileUrl', m."fileUrl",
                'fileExtension', m."fileExtension"
              )
            )
            FROM "forum"."AnswerMedia" m
            WHERE m."answerId" = a."id"
          )
        )
      )
      FROM "forum"."Answer" a
      INNER JOIN "user"."Profile" ap
        ON a."profileId" = ap.id
        AND ap."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = ap."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = ap."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE a."questionId" = qd.id
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      LIMIT 10
    ) as answers
  FROM question_data qd
`;

    if (!questionTempArr?.length) {
      throw new AppError('FMQUE008');
    }
    const questionTemp = questionTempArr[0];
    const question: ForumQuestionDetailWithAnswersI = {
      id: questionTemp.id,
      title: questionTemp.title,
      description: questionTemp.description,
      type: questionTemp.type,
      upvoteCount: questionTemp.upvoteCount,
      downvoteCount: questionTemp.downvoteCount,
      answerCount: questionTemp.answerCount,
      commentCount: questionTemp.commentCount,
      isLive: questionTemp.isLive,
      liveStartedAt: questionTemp.liveStartedAt,
      isSolved: questionTemp.isSolved,
      media: questionTemp?.media,
      vote: questionTemp?.vote,
      equipmentCategory: questionTemp.equipmentCategoryId
        ? {
            id: questionTemp.equipmentCategoryId,
            name: questionTemp.equipmentCategoryName,
            dataType: 'master',
          }
        : questionTemp.equipmentCategoryRawDataId
          ? {
              id: questionTemp.equipmentCategoryRawDataId,
              name: questionTemp.equipmentCategoryRawDataName,
              dataType: 'raw',
            }
          : null,
      equipmentModel: questionTemp.equipmentModelId
        ? {
            id: questionTemp.equipmentModelId,
            name: questionTemp.equipmentModelName,
            dataType: 'master',
          }
        : null,
      equipmentManufacturer: questionTemp.equipmentManufacturerId
        ? {
            id: questionTemp.equipmentManufacturerId,
            name: questionTemp.equipmentManufacturerName,
            dataType: 'master',
          }
        : null,
      createdAt: questionTemp.createdAt,
      canModify: questionTemp.profileId === selfProfileId,
      isEdited: questionTemp.isEdited,
      profile: !questionResult.isAnonymous
        ? { id: questionTemp.profileId, name: questionTemp.profileName, avatar: questionTemp.profileAvatar }
        : null,
      topics: questionTemp.topics,
      answers: questionTemp?.answers || null,
    };
    return question;
  },
  fetchMany: async (
    state: FastifyStateI,
    {
      cursorDate,
      departmentId,
      departmentDataType,
      isLive,
      myAnswered,
      myQuestion,
      myCommunity,
      myRecommended,
      pageSize,
      type,
    }: ForumQuestionFetchManyI,
  ): Promise<TotalCursorDateDataI<ForumQuestionFetchManyResultI>> => {
    pageSize = 100;
    const selfProfileId = state.profileId;
    const currentDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const joins: Prisma.Sql[] = [
      Prisma.sql`
        INNER JOIN "user"."Profile" u
          ON q."profileId" = u."id"
          AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1
          ON b1."blockerId" = ${selfProfileId}::uuid
          AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
          ON b2."blockerId" = u."id"
          AND b2."blockedId" = ${selfProfileId}::uuid
      `,
    ];
    const filters: Prisma.Sql[] = [];

    const baseFields = Prisma.sql`
      q."id",
      q."title",
      q."description",
      q."type",
      q."upvoteCount",
      q."downvoteCount",
      q."answerCount",
      q."commentCount",
      q."profileId",
      q."isSolved",
      q."isEdited",
      q."isAnonymous",
      q."liveStartedAt",
      q."isLive",
      (
        SELECT v."type" FROM "forum"."QuestionVote" v
        WHERE v."questionId" = q."id"
        AND v."profileId" = ${selfProfileId}::uuid
        LIMIT 1
      ) AS "vote",
      (
        SELECT json_agg(
          json_build_object(
            'fileUrl', m."fileUrl",
            'fileExtension', m."fileExtension"
          )
        )
        FROM "forum"."QuestionMedia" m
        WHERE m."questionId" = q."id"
      ) AS "media"
    `;
    const fieldsArr = [baseFields];
    switch (type) {
      case 'ALL': {
        fieldsArr.push(Prisma.sql`
        (
          SELECT json_agg(json_build_object(
            'topicId', t."id",
            'topicName', t."name",
            'topicRawDataId', trw."id",
            'topicRawDataName', trw."name"
          ))
          FROM "forum"."QuestionTopic" qt
          LEFT JOIN "forum"."Topic" t ON t."id" = qt."topicId"
          LEFT JOIN "rawData"."TopicRawData" trw ON trw."id" = qt."topicRawDataId"
          WHERE qt."questionId" = q."id"
        ) AS "topics"
      `);
        joins.push(Prisma.sql`
          LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
          LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
          LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
        `);
        fieldsArr.push(Prisma.sql`
          q."equipmentCategoryId",
          ec."name" AS "equipmentCategoryName",
          q."equipmentCategoryRawDataId",
          ecr."name" AS "equipmentCategoryRawDataName",
          q."equipmentModelId",
          em."name" AS "equipmentModelName",
          q."equipmentModelRawDataId",
          emr."name" AS "equipmentModelRawDataName",
          q."equipmentManufacturerId",
          emf."name" AS "equipmentManufacturerName",
          q."equipmentManufacturerRawDataId",
          emfr."name" AS "equipmentManufacturerRawDataName"
        `);
        if (myRecommended) {
          joins.push(Prisma.sql`
          LEFT JOIN "career"."ExperienceShip" xs
          ON (xs."shipImo" = q."shipImo" OR xs."shipRawDataImo" = q."shipRawDataImo")
          AND xs."profileId" = ${selfProfileId}::uuid
        `);
          filters.push(Prisma.sql`xs."id" IS NOT NULL`);
        }
        break;
      }
      case 'NORMAL': {
        fieldsArr.push(Prisma.sql`
        (
          SELECT json_agg(json_build_object(
            'topicId', t."id",
            'topicName', t."name",
            'topicRawDataId', trw."id",
            'topicRawDataName', trw."name"
          ))
          FROM "forum"."QuestionTopic" qt
          LEFT JOIN "forum"."Topic" t ON t."id" = qt."topicId"
          LEFT JOIN "rawData"."TopicRawData" trw ON trw."id" = qt."topicRawDataId"
          WHERE qt."questionId" = q."id"
        ) AS "topics"
      `);
        joins.push(Prisma.sql`
        LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
        LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
        LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
        LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
        LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
        LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
      `);
        break;
      }
      case 'TROUBLESHOOT': {
        joins.push(Prisma.sql`
          LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
          LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
          LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
        `);
        fieldsArr.push(Prisma.sql`
          q."equipmentCategoryId",
          ec."name" AS "equipmentCategoryName",
          q."equipmentCategoryRawDataId",
          ecr."name" AS "equipmentCategoryRawDataName",
          q."equipmentModelId",
          em."name" AS "equipmentModelName",
          q."equipmentModelRawDataId",
          emr."name" AS "equipmentModelRawDataName",
          q."equipmentManufacturerId",
          emf."name" AS "equipmentManufacturerName",
          q."equipmentManufacturerRawDataId",
          emfr."name" AS "equipmentManufacturerRawDataName"
        `);
        if (myRecommended) {
          joins.push(Prisma.sql`
            LEFT JOIN "career"."ExperienceShip" xs
            ON (xs."shipImo" = q."shipImo" OR xs."shipRawDataImo" = q."shipRawDataImo")
            AND xs."profileId" = ${selfProfileId}::uuid
          `);
          filters.push(Prisma.sql`xs."id" IS NOT NULL`);
        }
        break;
      }
    }

    if (departmentDataType === 'master') {
      joins.push(Prisma.sql`
        LEFT JOIN "company"."DepartmentAlternative" d ON d."id" = q."departmentAlternativeId"
      `);
      filters.push(Prisma.sql`q."departmentAlternativeId" = ${departmentId}::uuid`);
    } else if (departmentDataType === 'raw') {
      joins.push(Prisma.sql`
        LEFT JOIN "rawData"."DepartmentRawData" drw ON drw."id" = q."departmentRawDataId"
      `);
      filters.push(Prisma.sql`q."departmentRawDataId" = ${departmentId}::uuid`);
    }
    if (myAnswered) {
      joins.push(Prisma.sql`
        LEFT JOIN "forum"."Answer" a ON a."questionId" = q."id" AND a."profileId" = ${selfProfileId}::uuid
      `);
      filters.push(Prisma.sql`a."id" IS NOT NULL`);
    }

    if (myCommunity) {
      joins.push(Prisma.sql`
      LEFT JOIN "forum"."CommunityMember" cm ON q."communityId" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
    `);
      joins.push(Prisma.sql`
      LEFT JOIN "forum"."Community" c ON q."communityId" = c."id"
    `);
      filters.push(Prisma.sql`(cm."communityId" IS NOT NULL OR c."access" = 'GLOBAL')`);
    }

    if (cursorDate) {
      filters.push(Prisma.sql`q."liveStartedAt" < ${cursorDate}`);
    }
    if (['NORMAL', 'TROUBLESHOOT'].includes(type)) {
      filters.push(Prisma.sql`q."type" = ${type}::"forum"."QuestionTypeE"`);
    }
    if (isLive) {
      const config = (await AppConfig.AppConfigModule.fetchById({
        module: 'FORUM',
        subModule: 'QUESTION',
      })) as ForumQuestionConfigI;

      const Hours24Ago = subMsToDate({ date: currentDate, ms: config.liveExpiry });

      filters.push(Prisma.sql`q."liveStartedAt" >= ${Hours24Ago}`);
    }
    if (myQuestion) {
      filters.push(Prisma.sql`q."profileId" = ${selfProfileId}::uuid`);
    }

    const joinClause = joins.length ? Prisma.join(joins, '\n') : Prisma.empty;
    const whereClause = filters.length
      ? Prisma.sql`
      WHERE ${Prisma.join(filters, ' AND ')}`
      : Prisma.empty;

    // Queries
    const [questionsTemp, totalTemp] = await Promise.all([
      prismaPG.$queryRaw<ForumQuestionFetchManySQLI[]>`
      SELECT
        ${Prisma.join(fieldsArr, ', ')}
      FROM "forum"."Question" q
      ${joinClause}
      ${whereClause}
      ORDER BY q."liveStartedAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<{ count: number }[]>`
      SELECT COUNT(1) as count
      FROM "forum"."Question" q
      ${joinClause}
      ${whereClause}
    `,
    ]);

    // Format output
    const questions: ForumQuestionFetchManyResultI[] = [];
    let nextCursorDate: DateNullI = null;
    const total = Number(totalTemp?.[0]?.count ?? 0);

    if (questionsTemp?.length) {
      nextCursorDate = questionsTemp[questionsTemp.length - 1].liveStartedAt;
      questions.push(
        ...questionsTemp.map(
          (q) =>
            ({
              id: q.id,
              title: q.title,
              description: q.description,
              type: q.type,
              canModify: q.profileId === selfProfileId,
              upvoteCount: q.upvoteCount,
              downvoteCount: q.downvoteCount,
              answerCount: q.answerCount,
              commentCount: q.commentCount,
              isSolved: q.isSolved,
              isEdited: q.isEdited,
              isAnonymous: q.isAnonymous,
              liveStartedAt: q.liveStartedAt,
              isLive: q.isLive,
              vote: q.vote,
              equipmentCategory: q.equipmentCategoryId
                ? { id: q.equipmentCategoryId, name: q.equipmentCategoryName, dataType: 'master' }
                : q.equipmentCategoryRawDataId
                  ? { id: q.equipmentCategoryRawDataId, name: q.equipmentCategoryRawDataName, dataType: 'raw' }
                  : null,

              equipmentModel: q.equipmentModelId
                ? { id: q.equipmentModelId, name: q.equipmentModelName, dataType: 'master' }
                : q.equipmentModelRawDataId
                  ? { id: q.equipmentModelRawDataId, name: q.equipmentModelRawDataName, dataType: 'raw' }
                  : null,

              equipmentManufacturer: q.equipmentManufacturerId
                ? { id: q.equipmentManufacturerId, name: q.equipmentManufacturerName, dataType: 'master' }
                : q.equipmentManufacturerRawDataId
                  ? { id: q.equipmentManufacturerRawDataId, name: q.equipmentManufacturerRawDataName, dataType: 'raw' }
                  : null,

              topics: q?.topics?.map((topic) => TopicModule.transform(topic)) ?? null,
              media: q?.media,
            }) as ForumQuestionFetchManyResultI,
        ),
      );
    }
    return { data: questions, nextCursorDate, total };
  },
  updateLive: async (
    state: FastifyStateI,
    { isLive: requestedLiveStatus, questionId }: ForumQuestionUpdateLiveI,
  ): Promise<Pick<Question, 'isLive' | 'liveStartedAt'>> => {
    const selfProfileId = state.profileId;
    const [question, config] = await Promise.all([
      QuestionModule.fetchById({ id: questionId }, { liveStartedAt: true, isLive: true, profileId: true }),
      AppConfig.AppConfigModule.fetchById(
        { module: 'FORUM', subModule: 'QUESTION' },
        { config: true },
      ) as Promise<ForumQuestionConfigI>,
    ]);

    const currentDate = getCurrentDate();
    const expiryDate = question?.liveStartedAt
      ? addMsToDate({ date: question.liveStartedAt, ms: config.liveExpiry })
      : null;
    if (!isNullUndefined(expiryDate) && !question.isLive && currentDate > expiryDate) {
      const _questionTemp = await prismaPG.question.update({
        data: {
          isLive: false,
        },
        where: { id: questionId },
        select: { liveStartedAt: true },
      });
      question.isLive = false;
    }
    if (question?.profileId !== selfProfileId) {
      throw new AppError('FMQUE006');
    }
    const input: Prisma.QuestionUncheckedUpdateInput = {};
    if (requestedLiveStatus) {
      if (question?.isLive) {
        throw new AppError('FMQUE013');
      }
      input.isLive = true;
      input.liveStartedAt = getCurrentDate();
    } else {
      if (!question?.isLive) {
        throw new AppError('FMQUE014');
      }
      input.isLive = false;
      input.liveStartedAt = null;
    }
    const questionResult = await prismaPG.question.update({
      data: input,
      select: {
        isLive: true,
        liveStartedAt: true,
      },
      where: { id: questionId },
    });
    if (!questionResult) {
      throw new AppError('FMQUE015');
    }
    return questionResult;
  },
  search: async (
    state: FastifyStateI,
    { cursorId, pageSize, search }: ForumQuestionSearchI,
  ): Promise<ForumQuestionSearchResultI> => {
    const selfProfileId = state.profileId;
    const [totalResult, questionsResult] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1)::INTEGER AS total
      FROM "forum"."Question" q
      INNER JOIN "user"."Profile" u
        ON u."id" = q."profileId"
        AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        q."title" ILIKE ${'%' + search + '%'}
      AND q."cursorId" > ${cursorId}
      AND b1."blockerId" IS NULL
      AND b2."blockerId" IS NULL
      AND q."cursorId" > ${cursorId}
    `,
      prismaPG.$queryRaw<ForumQuestionSearchItemI[]>`
      SELECT
        q."id",
        q."title",
        q."cursorId"
      FROM "forum"."Question" q
      INNER JOIN "user"."Profile" u
        ON u."id" = q."profileId"
        AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        q."title" ILIKE ${'%' + search + '%'}
        AND q."cursorId" > ${cursorId}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      ORDER BY q."title" ASC
      LIMIT ${pageSize}
    `,
    ]);
    let nextCursorId: NumberNullI = null;
    if (questionsResult?.length) {
      nextCursorId = questionsResult[questionsResult.length - 1].cursorId;
    }
    return {
      nextCursorId,
      data: questionsResult,
      total: Number(totalResult?.[0]?.total || 0),
    } as ForumQuestionSearchResultI;
  },
  globalSearch: async (
    state: FastifyStateI,
    { search, page, pageSize }: GlobalSearchParamsI,
  ): Promise<GlobalSearchResponseI<GlobalSearchQuestionItemI>> => {
    const selfProfileId = state.profileId;
    const offset = page * pageSize;
    const searchTerm = `%${search.toLowerCase()}%`;

    // const tsSearchTerm = search.replace(/[!&|():*]/g, ' ').trim().split(/\s+/).join(' & ');

    try {
      const [totalResult, questionsResult] = await Promise.all([
        prismaPG.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(DISTINCT q."id") as count
          FROM "forum"."Question" q
          INNER JOIN "forum"."Community" c ON q."communityId" = c."id"
          INNER JOIN "user"."Profile" u ON q."profileId" = u."id" AND u."status" = 'ACTIVE'
          LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
          LEFT JOIN "network"."BlockedProfile" b1 ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
          LEFT JOIN "network"."BlockedProfile" b2 ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
          LEFT JOIN "company"."DepartmentAlternative" da ON q."departmentAlternativeId" = da."id"
          LEFT JOIN "rawData"."DepartmentRawData" drd ON q."departmentRawDataId" = drd."id"
          LEFT JOIN "ship"."EquipmentCategory" ec ON q."equipmentCategoryId" = ec."id"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecrd ON q."equipmentCategoryRawDataId" = ecrd."id"
          LEFT JOIN "ship"."EquipmentModel" em ON q."equipmentModelId" = em."id"
          LEFT JOIN "rawData"."EquipmentModelRawData" emrd ON q."equipmentModelRawDataId" = emrd."id"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON q."equipmentManufacturerId" = emf."id"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfrd ON q."equipmentManufacturerRawDataId" = emfrd."id"
          LEFT JOIN "forum"."QuestionTopic" qt ON q."id" = qt."questionId"
          LEFT JOIN "forum"."Topic" t ON qt."topicId" = t."id"
          LEFT JOIN "rawData"."TopicRawData" trd ON qt."topicRawDataId" = trd."id"
          WHERE (
            (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
            OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
          )
          AND b1."blockerId" IS NULL
          AND b2."blockerId" IS NULL
          AND (
            LOWER(q."title") LIKE ${searchTerm}
            OR LOWER(q."description") LIKE ${searchTerm}
            OR LOWER(da."name") LIKE ${searchTerm}
            OR LOWER(drd."name") LIKE ${searchTerm}
            OR LOWER(ec."name") LIKE ${searchTerm}
            OR LOWER(ecrd."name") LIKE ${searchTerm}
            OR LOWER(em."name") LIKE ${searchTerm}
            OR LOWER(emrd."name") LIKE ${searchTerm}
            OR LOWER(emf."name") LIKE ${searchTerm}
            OR LOWER(emfrd."name") LIKE ${searchTerm}
            OR LOWER(t."name") LIKE ${searchTerm}
            OR LOWER(trd."name") LIKE ${searchTerm}
          )
        `,
        prismaPG.$queryRaw<GlobalSearchQuestionItemI[]>`
          WITH ranked_questions AS (
            SELECT DISTINCT q."id",
            CASE
              WHEN LOWER(q."title") LIKE ${searchTerm} THEN 1
              WHEN LOWER(t."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(trd."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(da."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(drd."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(ec."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(ecrd."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(em."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(emrd."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(emf."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(emfrd."name") LIKE ${searchTerm} THEN 2
              WHEN LOWER(q."description") LIKE ${searchTerm} THEN 3
              ELSE 4
            END AS relevance_score,
            q."createdAt"
            FROM "forum"."Question" q
            INNER JOIN "forum"."Community" c ON q."communityId" = c."id"
            INNER JOIN "user"."Profile" u ON q."profileId" = u."id" AND u."status" = 'ACTIVE'
            LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
            LEFT JOIN "network"."BlockedProfile" b1 ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
            LEFT JOIN "network"."BlockedProfile" b2 ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
            LEFT JOIN "company"."DepartmentAlternative" da ON q."departmentAlternativeId" = da."id"
            LEFT JOIN "rawData"."DepartmentRawData" drd ON q."departmentRawDataId" = drd."id"
            LEFT JOIN "ship"."EquipmentCategory" ec ON q."equipmentCategoryId" = ec."id"
            LEFT JOIN "rawData"."EquipmentCategoryRawData" ecrd ON q."equipmentCategoryRawDataId" = ecrd."id"
            LEFT JOIN "ship"."EquipmentModel" em ON q."equipmentModelId" = em."id"
            LEFT JOIN "rawData"."EquipmentModelRawData" emrd ON q."equipmentModelRawDataId" = emrd."id"
            LEFT JOIN "ship"."EquipmentManufacturer" emf ON q."equipmentManufacturerId" = emf."id"
            LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfrd ON q."equipmentManufacturerRawDataId" = emfrd."id"
            LEFT JOIN "forum"."QuestionTopic" qt ON q."id" = qt."questionId"
            LEFT JOIN "forum"."Topic" t ON qt."topicId" = t."id"
            LEFT JOIN "rawData"."TopicRawData" trd ON qt."topicRawDataId" = trd."id"
            WHERE (
              (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
              OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
            )
            AND b1."blockerId" IS NULL
            AND b2."blockerId" IS NULL
            AND (
              LOWER(q."title") LIKE ${searchTerm}
              OR LOWER(q."description") LIKE ${searchTerm}
              OR LOWER(da."name") LIKE ${searchTerm}
              OR LOWER(drd."name") LIKE ${searchTerm}
              OR LOWER(ec."name") LIKE ${searchTerm}
              OR LOWER(ecrd."name") LIKE ${searchTerm}
              OR LOWER(em."name") LIKE ${searchTerm}
              OR LOWER(emrd."name") LIKE ${searchTerm}
              OR LOWER(emf."name") LIKE ${searchTerm}
              OR LOWER(emfrd."name") LIKE ${searchTerm}
              OR LOWER(t."name") LIKE ${searchTerm}
              OR LOWER(trd."name") LIKE ${searchTerm}
            )
          )
          SELECT
            q."id",
            q."title",
            q."description",
            q."type",
            q."communityId",
            c."name" as "communityName",
            q."departmentAlternativeId",
            COALESCE(da."name", drd."name") as "departmentName",
            q."equipmentCategoryId",
            COALESCE(ec."name", ecrd."name") as "equipmentCategoryName",
            q."equipmentModelId",
            COALESCE(em."name", emrd."name") as "equipmentModelName",
            q."equipmentManufacturerId",
            COALESCE(emf."name", emfrd."name") as "equipmentManufacturerName",
            q."answerCount",
            q."upvoteCount",
            q."createdAt",
            u."name" as "profileName",
            ARRAY[]::text[] as "matchedFields",
            (
              SELECT COALESCE(
                json_agg(
                  json_build_object(
                    'id', t."id",
                    'name', t."name",
                    'type', 'master'
                  )
                ) FILTER (WHERE t."id" IS NOT NULL),
                '[]'::json
              )
              FROM "forum"."QuestionTopic" qt
              LEFT JOIN "master"."Topic" t ON qt."topicId" = t."id"
              WHERE qt."questionId" = q."id"
            ) as "topics"
          FROM "forum"."Question" q
          INNER JOIN ranked_questions rq ON q."id" = rq."id"
          INNER JOIN "forum"."Community" c ON q."communityId" = c."id"
          INNER JOIN "user"."Profile" u ON q."profileId" = u."id"
          LEFT JOIN "company"."DepartmentAlternative" da ON q."departmentAlternativeId" = da."id"
          LEFT JOIN "rawData"."DepartmentRawData" drd ON q."departmentRawDataId" = drd."id"
          LEFT JOIN "ship"."EquipmentCategory" ec ON q."equipmentCategoryId" = ec."id"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecrd ON q."equipmentCategoryRawDataId" = ecrd."id"
          LEFT JOIN "ship"."EquipmentModel" em ON q."equipmentModelId" = em."id"
          LEFT JOIN "rawData"."EquipmentModelRawData" emrd ON q."equipmentModelRawDataId" = emrd."id"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON q."equipmentManufacturerId" = emf."id"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfrd ON q."equipmentManufacturerRawDataId" = emfrd."id"
          ORDER BY rq.relevance_score ASC, rq."createdAt" DESC
          LIMIT ${pageSize}
          OFFSET ${offset}
        `,
      ]);

      const total = Number(totalResult?.[0]?.count || 0);

     
      const data = questionsResult.map((item) => {
        const matchedFields: string[] = [];
        if (item.title && item.title.toLowerCase().includes(search.toLowerCase())) {
          matchedFields.push('title');
        }
        if (item.description && item.description.toLowerCase().includes(search.toLowerCase())) {
          matchedFields.push('description');
        }
        if (item.departmentName && item.departmentName.toLowerCase().includes(search.toLowerCase())) {
          matchedFields.push('department');
        }
        if (item.equipmentCategoryName && item.equipmentCategoryName.toLowerCase().includes(search.toLowerCase())) {
          matchedFields.push('equipmentCategory');
        }
        if (item.equipmentModelName && item.equipmentModelName.toLowerCase().includes(search.toLowerCase())) {
          matchedFields.push('equipmentModel');
        }
        if (item.equipmentManufacturerName && item.equipmentManufacturerName.toLowerCase().includes(search.toLowerCase())) {
          matchedFields.push('equipmentManufacturer');
        }
        
        if (item.topics && Array.isArray(item.topics)) {
          const hasTopicMatch = item.topics.some((topic: any) =>
            topic.name && topic.name.toLowerCase().includes(search.toLowerCase())
          );
          if (hasTopicMatch) {
            matchedFields.push('topics');
          }
        }

        return {
          ...item,
          matchedFields,
        };
      });

      return {
        data,
        total,
      };
    } catch (error) {
      console.log(error, '------------------->>>>>>>>>>>>>>>>>>')
      errorHandler(error);
      throw error;
    }
  },
};
