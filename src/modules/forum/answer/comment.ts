import type { FastifyStateI } from '@interfaces/common/declaration';
import type { ForumCommentCreateOneI } from '@schemas/forum/comment';
import { AnswerModule } from './answer';
import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import { AnswerComment, Prisma } from '@prisma/postgres';
import type { RouteParamsI } from '@schemas/common/common';
import type { IdCursorIdI, TotalCursorDataI } from '@interfaces/common/data';
import User from '@modules/user';
import type { ForumCommentExternalI } from '@interfaces/forum/comment';
import { CommunityModule } from '../community/community';
import type {
  ForumAnswerCommentDeleteOneI,
  ForumAnswerCommentFetchManyI,
  ForumAnswerCommentFetchRepliesI,
} from '@schemas/forum/answerComment';
import type {
  ForumAnswerCommentFetchManyCoreReplyItemI,
  ForumAnswerCommentFetchManyReplySQLI,
  ForumAnswerCommentFetchManyResultI,
  ForumAnswerCommentFetchManySQLI,
} from '@interfaces/forum/answerComment';
import type { TotalI } from '@interfaces/common/db';
import { isNumber, pick } from '@utils/data/object';
import { isNullUndefined } from '@utils/data/data';
import { errorHandler } from '@utils/errors/handler';

export const AnswerCommentModule = {
  fetchById: async (
    { id }: RouteParamsI,
    select: Prisma.AnswerCommentSelect = {
      id: true,
    },
    isThrowingError: boolean = true,
  ): Promise<AnswerComment> => {
    const answerResult = await prismaPG.answerComment.findUnique({
      select,
      where: {
        id,
      },
    });
    if (isThrowingError && !answerResult) {
      throw new AppError('FMACMT002');
    }
    return answerResult;
  },
  createOne: async (
    state: FastifyStateI,
    { answerId, parentCommentId, text }: ForumCommentCreateOneI,
  ): Promise<IdCursorIdI> => {
    const selfProfileId = state.profileId;
    const [answerResult, parentCommentResult] = await Promise.all([
      AnswerModule.fetchById({ id: answerId }, { communityId: true, questionId: true }),
      parentCommentId
        ? prismaPG.answerComment.findUnique({
            where: { id: parentCommentId, answerId },
            select: { id: true, parentCommentId: true, replyCount: true },
          })
        : null,
    ]);
    if (parentCommentId && !parentCommentResult) {
      throw new AppError('FMACMT003');
    }
    const questionId = answerResult.questionId;
    const communityId = answerResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    const input: Prisma.AnswerCommentUncheckedCreateInput = {
      questionId,
      profileId: selfProfileId,
      text,
      answerId,
      communityId,
    };
    if (parentCommentId) {
      input.parentCommentId = parentCommentId;
    }
    const [commentResult, _parentCommentResult, _answerResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.answerComment.create({
            data: input,
            select: { id: true, cursorId: true },
          }),
          parentCommentResult
            ? txn.answerComment.update({
                select: { replyCount: true, id: true },
                data: !isNullUndefined(parentCommentResult?.replyCount)
                  ? {
                      replyCount: {
                        increment: 1,
                      },
                    }
                  : { replyCount: 1 },
                where: {
                  id: parentCommentId,
                },
              })
            : null,
          txn.answer.update({
            data: {
              commentCount: {
                increment: 1,
              },
            },
            where: {
              id: answerId,
            },
            select: { id: true },
          }),
        ]),
    );

    if (!commentResult) {
      throw new AppError('FMACMT001');
    }
    return { id: commentResult.id, cursorId: Number(commentResult.cursorId) };
  },
  fetchMany: async (
    state: FastifyStateI,
    { answerId, cursorId, pageSize }: ForumAnswerCommentFetchManyI,
  ): Promise<TotalCursorDataI<ForumAnswerCommentFetchManyResultI>> => {
    const selfProfileId = state.profileId;
    const answerResult = await AnswerModule.fetchById({ id: answerId }, { communityId: true, profileId: true });
    const communityId = answerResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }

    const [commentsTemp, commentsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<ForumAnswerCommentFetchManySQLI[]>`
      SELECT
        c."id",
        c."cursorId",
        c."text",
        c."replyCount",
        c."createdAt",
        c."profileId",
        u."name" AS "profileName",
        u."avatar" AS "profileAvatar",
        (
          SELECT json_agg(reply_data)
          FROM (
            SELECT
              r."id",
              r."cursorId",
              r."text",
              r."replyCount",
              r."createdAt",
              r."profileId",
              ru."name" AS "profileName",
              ru."avatar" AS "profileAvatar"
            FROM "forum"."AnswerComment" r
            INNER JOIN "user"."Profile" ru ON ru."id" = r."profileId"
            LEFT JOIN "network"."BlockedProfile" br1
              ON br1."blockerId" = ${selfProfileId}::uuid
              AND br1."blockedId" = ru."id"
            LEFT JOIN "network"."BlockedProfile" br2
              ON br2."blockerId" = ru."id"
              AND br2."blockedId" = ${selfProfileId}::uuid
            WHERE
              r."parentCommentId" = c."id"
              AND br1."blockerId" IS NULL
              AND br2."blockerId" IS NULL
            ORDER BY r."createdAt" DESC
            LIMIT 2
          ) AS reply_data
        ) AS replies
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."answerId" = ${answerId}::uuid
        AND c."parentCommentId" IS NULL
        ${cursorId ? Prisma.sql` AND c."cursorId" < ${cursorId}` : Prisma.empty}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      ORDER BY c."createdAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1) AS "total"
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."answerId" = ${answerId}::uuid
        AND c."parentCommentId" IS NULL
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
    `,
    ]);

    const commentsResult: ForumAnswerCommentFetchManyResultI[] = [];
    let nextCursorId = null;
    if (commentsTemp?.length) {
      commentsResult.push(
        ...commentsTemp.map(
          (item) =>
            ({
              ...pick(item, ['id', 'text', 'replyCount', 'createdAt']),
              cursorId: Number(item.cursorId),
              canDelete: item.profileId === selfProfileId,
              profile: {
                id: item.profileId,
                name: item.profileName,
                avatar: item.profileAvatar,
              },
              replies:
                item?.replies?.map(
                  (reply) =>
                    ({
                      id: reply.id,
                      text: reply.text,
                      cursorId: reply.cursorId,
                      createdAt: reply.createdAt,
                      profile: {
                        id: reply.profileId,
                        name: reply.profileName,
                        avatar: reply.profileAvatar,
                      },
                    }) as ForumAnswerCommentFetchManyCoreReplyItemI,
                ) || null,
            }) as ForumAnswerCommentFetchManyResultI,
        ),
      );
      const lastComment = commentsTemp[commentsTemp.length - 1];
      nextCursorId =
        commentsResult.length > 0 && lastComment
          ? Number(lastComment.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: commentsResult,
      total: Number(commentsTotalResult?.[0]?.total || 0),
      nextCursorId,
    };
  },
  fetchReplies: async (
    state: FastifyStateI,
    { answerId, parentCommentId, cursorId, pageSize }: ForumAnswerCommentFetchRepliesI,
  ): Promise<TotalCursorDataI<ForumAnswerCommentFetchManyCoreReplyItemI>> => {
    const selfProfileId = state.profileId;

    const [answerResult, _parentCommentResult] = await Promise.all([
      AnswerModule.fetchById({ id: answerId }, { communityId: true, profileId: true }),
      parentCommentId ? AnswerCommentModule.fetchById({ id: parentCommentId }) : null,
    ]);

    const communityId = answerResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }

    const [commentsTemp, commentsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<ForumAnswerCommentFetchManyReplySQLI[]>`
      SELECT
        c."id",
        c."cursorId",
        c."text",
        c."createdAt",
        c."profileId",
        u."name" AS "profileName",
        u."avatar" AS "profileAvatar"
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
      WHERE
        c."parentCommentId" = ${parentCommentId}::uuid
        ${cursorId ? Prisma.sql` AND c."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY c."createdAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1) AS "total"
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."parentCommentId" = ${parentCommentId}::uuid
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
    `,
    ]);
    const commentsResult: ForumAnswerCommentFetchManyCoreReplyItemI[] = [];
    let nextCursorId = null;
    if (commentsTemp?.length) {
      commentsResult.push(
        ...commentsTemp.map(
          (item) =>
            ({
              id: item.id,
              text: item.text,
              createdAt: item.createdAt,
              cursorId: Number(item.cursorId),
              profile: {
                id: item.profileId,
                name: item.profileName,
                avatar: item.profileAvatar,
              },
              canDelete: item.profileId === selfProfileId,
            }) as ForumAnswerCommentFetchManyCoreReplyItemI,
        ),
      );
      const lastComment = commentsTemp[commentsTemp.length - 1];
      nextCursorId =
        commentsResult.length > 0 && lastComment
          ? Number(lastComment.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: commentsResult,
      total: Number(commentsTotalResult?.[0]?.total || 0),
      nextCursorId,
    };
  },
  deleteOne: async (state: FastifyStateI, { id: commentId }: ForumAnswerCommentDeleteOneI): Promise<void> => {
    try {
      const selfProfileId = state.profileId;

      const existingCommentResult = await prismaPG.answerComment.findUnique({
        where: { id: commentId },
        select: {
          communityId: true,
          profileId: true,
          replyCount: true,
          Answer: {
            select: {
              id: true,
              commentCount: true,
              Community: {
                select: {
                  CommunityMember: {
                    select: { type: true },
                    where: {
                      profileId: selfProfileId,
                      type: {
                        in: ['ADMIN', 'MODERATOR'],
                      },
                    },
                  },
                },
              },
            },
          },
          Parent: {
            select: { id: true, replyCount: true },
          },
        },
      });
      if (!existingCommentResult) {
        throw new AppError('FQCM003');
      }
      const communityId = existingCommentResult.communityId;
      await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

      const isCommentAuthor = existingCommentResult.profileId === selfProfileId;

      const isCommunityAdminOrModerator = existingCommentResult.Answer.Community.CommunityMember.length > 0;

      if (!(isCommentAuthor || isCommunityAdminOrModerator)) {
        throw new AppError('FMACMT007');
      }
      const [deletedCommentResult, _updatedAnswerResult, _updatedParentCommentResult] = await prismaPG.$transaction(
        async (txn) =>
          await Promise.all([
            txn.answerComment.delete({
              select: { id: true },
              where: { id: commentId },
            }),
            existingCommentResult.Answer?.commentCount > 0
              ? txn.answer.update({
                  data: {
                    commentCount: {
                      decrement: isNumber(existingCommentResult.replyCount) ? existingCommentResult.replyCount + 1 : 1,
                    },
                  },
                  select: { commentCount: true },
                  where: {
                    id: existingCommentResult.Answer.id,
                  },
                })
              : null,
            existingCommentResult?.Parent?.id && existingCommentResult?.Parent?.replyCount > 0
              ? txn.answerComment.update({
                  data: {
                    replyCount: { decrement: 1 },
                  },
                  where: {
                    id: existingCommentResult.Parent?.id,
                  },
                })
              : null,
          ]),
      );
      if (!deletedCommentResult) {
        throw new AppError('FQCM008');
      }
    } catch (error) {
      errorHandler(error);
    }
  },
  transformComment: (item): ForumCommentExternalI => ({
    id: item.id,
    text: item.text,
    cursorId: Number(item.cursorId.toString()),
    replyCount: item.replyCount,
    createdAt: item.createdAt,
    Profile: User.ProfileModule.transformProfile(item.Profile),
  }),
};
