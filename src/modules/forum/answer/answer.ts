import type { FastifyStateI } from '@interfaces/common/declaration';
import type { AnswerUpdateStatusI, ForumAnswerCreateOneI, ForumAnswerFetchManyI } from '@schemas/forum/answer';
import { CommunityModule } from '../community/community';
import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import { Answer, Prisma } from '@prisma/postgres';
import { QuestionModule } from '../question/question';
import type { RouteParamsI } from '@schemas/common/common';
import type {
  ForumAnswerFetchManyResultI,
  ForumAnswerI,
  ForumAnswerWithProfileI,
  ForumAnswerWithProfileSQLI,
} from '@interfaces/forum/answer';
import { pick } from '@utils/data/object';
import CommunityMemberModule from '../member';
import { MemberTypeI } from '@consts/forum/member';
import { TotalI } from '@interfaces/common/db';

export const AnswerModule = {
  fetchById: async (
    { id }: Pick<Prisma.AnswerWhereUniqueInput, 'id'>,
    select: Prisma.AnswerSelect = {
      id: true,
    },
  ): Promise<Answer> => {
    const answerResult = await prismaPG.answer.findUnique({
      select,
      where: {
        id,
      },
    });
    if (!answerResult) {
      throw new AppError('FMANS002');
    }
    return answerResult;
  },
  createOne: async (
    state: FastifyStateI,
    { text, questionId, files }: ForumAnswerCreateOneI,
  ): Promise<ForumAnswerI> => {
    const selfProfileId = state.profileId;
    const questionResult = await QuestionModule.fetchById({ id: questionId }, { communityId: true });
    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const input: Prisma.AnswerUncheckedCreateInput = {
      profileId: selfProfileId,
      communityId,
      text,
      questionId,
    };
    if (files?.length) {
      input.AnswerMedia = {
        createMany: {
          data: files.map((file) => ({ communityId, fileUrl: file.fileUrl, fileExtension: file.fileExtension })),
        },
      };
    }
    const [answerResult, _questionResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.answer.create({
            data: input,
            select: { id: true, cursorId: true },
          }),
          txn.question.update({
            data: {
              answerCount: {
                increment: 1,
              },
            },
            where: {
              id: questionId,
            },
            select: { id: true },
          }),
        ]),
    );

    if (!answerResult) {
      throw new AppError('FMANS001');
    }
    return { cursorId: Number(answerResult.cursorId), id: answerResult.id };
  },
  deleteOne: async (state: FastifyStateI, { id: answerId }: RouteParamsI): Promise<void> => {
    const selfProfileId = state.profileId;
    const answerResult = await prismaPG.answer.findUnique({
      where: { id: answerId },
      select: {
        profileId: true,
        questionId: true,
        Community: {
          select: {
            CommunityMember: {
              select: {
                profileId: true,
              },
              where: {
                profileId: selfProfileId,
                type: {
                  in: ['ADMIN', 'CONTRIBUTOR'],
                },
              },
            },
          },
        },
      },
    });
    const isAnswerAuthor = answerResult.profileId === selfProfileId;
    const isCommunityAdminOrModerator = answerResult.Community.CommunityMember.length > 0;
    if (!(isAnswerAuthor || isCommunityAdminOrModerator)) {
      throw new AppError('FMANS009');
    }

    const deletedAnswerResult = await prismaPG.$transaction(async (txn) => {
      const [deletedAnswerResult, questionResult] = await Promise.all([
        txn.answer.delete({
          select: { id: true },
          where: { id: answerId },
        }),
        txn.question.findUnique({
          select: { answerCount: true },
          where: {
            id: answerResult.questionId,
          },
        }),
      ]);
      if (questionResult?.answerCount > 0) {
        const _updatedQuestionResult = await txn.question.update({
          data: {
            answerCount: {
              decrement: 1,
            },
          },
          where: {
            id: answerResult.questionId,
          },
          select: { id: true },
        });
      }
      return deletedAnswerResult;
    });

    if (!deletedAnswerResult) {
      throw new AppError('FMANS003');
    }
  },
  fetchMany: async (
    state: FastifyStateI,
    { cursorId: cursorIdP, pageSize, questionId }: ForumAnswerFetchManyI,
  ): Promise<ForumAnswerFetchManyResultI> => {
    const selfProfileId = state.profileId;

    let cursorId: number | null = null;
    if (typeof cursorIdP === 'number' && cursorIdP > 0) {
      cursorId = Number(cursorIdP);
    }
    const questionResult = await QuestionModule.fetchById({ id: questionId }, { communityId: true, profileId: true });

    const communityId = questionResult.communityId;

    const [_communityResult, memberResult] = await Promise.all([
      CommunityModule.fetchById({
        id: communityId,
      }),
      CommunityMemberModule.fetchMember(
        {
          communityId,
          profileId: selfProfileId,
        },
        false,
      ),
    ]);
    const isQuestionAuthorOrAdminModerator =
      questionResult.profileId === selfProfileId ||
      (['ADMIN', 'MODERATOR'] as MemberTypeI[]).includes(memberResult?.type);

    const [answersTotalResult, answersResultTemp] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
            SELECT
              COUNT(1) AS "total"
            FROM
              "forum"."Answer" a
            INNER JOIN "user"."Profile" u
              ON u."id" = a."profileId"
              AND u."status" = 'ACTIVE'
            LEFT JOIN "network"."BlockedProfile" b1
              ON b1."blockerId" = ${selfProfileId}::uuid
              AND b1."blockedId" = u."id"
            LEFT JOIN "network"."BlockedProfile" b2
              ON b2."blockerId" = u."id"
              AND b2."blockedId" = ${selfProfileId}::uuid
            WHERE
              a."questionId" = ${questionId}::uuid
              AND b1."blockerId" IS NULL
              AND b2."blockerId" IS NULL
          `,
      prismaPG.$queryRaw<ForumAnswerWithProfileSQLI[]>`
          SELECT
            a."id",
            a."cursorId",
            CASE
              WHEN LENGTH(a."text") > 150
              THEN LEFT(a."text", 150) || '...'
              ELSE a."text"
              END AS "text",
              LENGTH(a."text") > 150 AS "isTextTruncated",
            a."upvoteCount",
            a."downvoteCount",
            a."commentCount",
            a."status",
            (
              SELECT v."type" FROM "forum"."AnswerVote" v
              WHERE v."answerId" = a."id"
              AND v."profileId" = ${selfProfileId}::uuid
              LIMIT 1
            ) AS "vote",
            (
              SELECT json_agg(
                json_build_object(
                  'fileUrl', m."fileUrl",
                  'fileExtension', m."fileExtension"
                )
              )
              FROM "forum"."AnswerMedia" m
              WHERE m."answerId" = a."id"
            ) AS "media",
            u."id" AS "profileId",
            u."name" AS "profileName",
            u."avatar" AS "profileAvatar"
          FROM
          "forum"."Answer" a
          INNER JOIN
          "user"."Profile" u
          ON
          a."profileId" = u."id"
          AND u."status" = 'ACTIVE'
          LEFT JOIN
          "network"."BlockedProfile" b1
          ON
          b1."blockerId" = ${selfProfileId}::uuid
          AND
          b1."blockedId" = u."id"
          LEFT JOIN
          "network"."BlockedProfile" b2
          ON
          b2."blockerId" = u."id"
          AND
          b2."blockedId" = ${selfProfileId}::uuid
          WHERE
          a."questionId" = ${questionId}::uuid
          AND ${
            cursorId
              ? Prisma.sql`
          a."cursorId" < ${cursorId}
          AND
          `
              : Prisma.empty
          }
          b1."blockerId" IS NULL
          AND
          b2."blockerId" IS NULL
          ORDER BY
          a."createdAt" DESC
          LIMIT ${pageSize}
        `,
    ]);

    const answersResult: ForumAnswerWithProfileI[] = [];
    let nextCursorId = null;
    if (answersResultTemp?.length) {
      answersResult.push(
        ...answersResultTemp.map(
          (item) =>
            ({
              ...pick(item, [
                'id',
                'upvoteCount',
                'downvoteCount',
                'commentCount',
                'text',
                'isTextTruncated',
                'vote',
                'status',
                'media',
              ]),
              cursorId: Number(item.cursorId),
              canModify: item.profileId === selfProfileId,
              canUpdateStatus: item.profileId === selfProfileId ? false : isQuestionAuthorOrAdminModerator,
              profile: {
                id: item.profileId,
                name: item.profileName,
                avatar: item.profileAvatar,
              },
            }) as ForumAnswerWithProfileI,
        ),
      );
      const lastAnswer = answersResultTemp[answersResultTemp.length - 1];
      nextCursorId =
        answersResult.length > 0 && lastAnswer
          ? Number(lastAnswer.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: answersResult,
      total: Number(answersTotalResult?.[0]?.total || 0),
      nextCursorId,
    };
  },
  updateStatus: async (
    state: FastifyStateI,
    { id: answerId, status: requestedStatus }: AnswerUpdateStatusI,
  ): Promise<void> => {
    const selfProfileId = state.profileId;

    // Fetch the answer with necessary related data
    const answer = await prismaPG.answer.findUnique({
      where: { id: answerId },
      select: {
        status: true,
        questionId: true,
        profileId: true,
        Community: {
          select: {
            id: true,
            CommunityMember: {
              select: {
                type: true,
              },
              where: {
                profileId: selfProfileId,
                type: {
                  in: ['ADMIN', 'MODERATOR'],
                },
              },
            },
          },
        },
      },
    });
    if (answer.status === requestedStatus) {
      throw new AppError('FMANS007');
    }
    if (selfProfileId === answer.profileId) {
      throw new AppError('FMANS008');
    }
    // Fetch the question to get the asker
    const question = await QuestionModule.fetchById(
      { id: answer.questionId },
      {
        profileId: true, // question asker
        communityId: true,
      },
    );
    const questionId = answer.questionId;

    const isQuestionAuthor = question.profileId === selfProfileId;
    const isCommunityAdminOrModerator = answer.Community.CommunityMember.length > 0;
    if (!(isQuestionAuthor || isCommunityAdminOrModerator)) {
      throw new AppError('FMANS009');
    }

    await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.answer.update({
            data: {
              status: requestedStatus,
            },
            where: { id: answerId },
          }),
          txn.question.update({
            data: {
              isSolved: requestedStatus === 'VERIFIED_SOLUTION',
            },
            where: {
              id: questionId,
            },
          }),
        ]),
    );
  },
  fetchOne: async (
    state: FastifyStateI,
    { id }: Pick<Prisma.AnswerWhereUniqueInput, 'id'>,
    select: Prisma.AnswerSelect = {
      id: true,
    },
  ): Promise<Answer> => {
    const selfProfileId = state.profileId;
    const answer = await AnswerModule.fetchById({ id }, { ...select, communityId: true });
    await CommunityModule.isPermitted({ communityId: answer.communityId, profileId: selfProfileId });
    return answer;
  },
};
