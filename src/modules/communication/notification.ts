import { prismaPG } from '@config/db';
import type { NotificationFetchManyResultI } from '@interfaces/communication/notification';
import type { PostForNotificationI } from '@interfaces/feed/post';

import {
  NotificationCreateOneSchema,
  type NotificationCreateOneI,
  type NotificationFetchManyI,
} from '@schemas/communication/notification';
import AppError from '@classes/AppError';
import CoreCommunicationModule from './communication';
import { toAny } from '@utils/data/object';
import { FastifyStateI } from '@interfaces/common/declaration';

const NotificationModule = {
  createOne: async (params: NotificationCreateOneI) => {
    try {
      const { error: notificationCreateOneError } = NotificationCreateOneSchema.safeParse(params);

      if (notificationCreateOneError) {
        throw new AppError('NF001');
      }
      const notificationCreateOneData = toAny(params);
      await CoreCommunicationModule.createOne(notificationCreateOneData?.topic, {
        category: 'NOTIFICATION',
        profileId: notificationCreateOneData?.profileId,
        profileIds: notificationCreateOneData?.profileIds,
        firebaseTopic: notificationCreateOneData?.firebaseTopic,
        notification: {
          actorProfileId: notificationCreateOneData?.actorProfileId,
          actorProfileName: notificationCreateOneData?.actorProfileName,
          commentId: notificationCreateOneData?.commentId,
          ids: notificationCreateOneData?.ids,
          parentCommentId: notificationCreateOneData?.parentCommentId,
          postId: notificationCreateOneData?.postId,
          postText: notificationCreateOneData?.postText,
          profileId: notificationCreateOneData?.profileId,
          type: notificationCreateOneData?.type,
          requestId: notificationCreateOneData?.requestId,
        },
      });
    } catch (_error) {
      //
    }
  },
  fetchMany: async (_state: FastifyStateI, data: NotificationFetchManyI): Promise<NotificationFetchManyResultI> => {
    const actorProfileIds = Array.from(new Set(data.items.map((item) => item.actorProfileId).filter(Boolean)));
    const postIds = Array.from(new Set(data.items.map((item) => item.postId).filter(Boolean)));

    const [profiles, posts] = await Promise.all([
      actorProfileIds.length
        ? prismaPG.profile.findMany({
            select: {
              id: true,
              avatar: true,
              name: true,
            },
            where: {
              id: { in: actorProfileIds },
            },
          })
        : [],
      postIds.length
        ? prismaPG.$queryRaw<PostForNotificationI[]>`
            SELECT
              p.id,
              LEFT(p.caption, 20) AS "caption",
              (
                SELECT pm."fileUrl"
                FROM "feed"."PostMedia" pm
                WHERE pm."postId" = p.id
                ORDER BY pm."createdAt" ASC
                LIMIT 1
              ) AS "image"
            FROM
              "feed"."Post" p
            WHERE
              p.id = ANY(${postIds}::uuid[])
          `
        : [],
    ]);

    return {
      profiles,
      posts,
    };
  },
};
export default NotificationModule;
