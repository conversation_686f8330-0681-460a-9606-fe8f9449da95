import AppError from '@classes/AppError';
import { KafkaTopicE, type KafkaTopicI } from '@consts/kafka';
import { CommunicationNotificationSchema } from '@schemas/communication/communication';
import type { CommunicationNotificationBaseI } from '@schemas/communication/communication';
import { deepCleanObj } from '@utils/data/object';
import KafkaService from 'services/kafka';

const CoreCommunicationModule = {
  createOne: async (topic: KafkaTopicI, params: CommunicationNotificationBaseI) => {
    try {
      const { data: kafkaTopicData, error: kafkaTopicError } = KafkaTopicE.safeParse(topic);

      if (kafkaTopicError) {
        throw new AppError('NF013');
      }
      const { error: communicationNotificationBaseError } = CommunicationNotificationSchema.safeParse(params);

      if (communicationNotificationBaseError) {
        throw new AppError('CMN003');
      }
      const data = deepCleanObj(params);

      await KafkaService.instance.sendMessage(kafkaTopicData, data);
    } catch (_error) {
      //
    }
  },
};
export default CoreCommunicationModule;
