import type { FastifyReply, FastifyRequest } from 'fastify';
import AppError from '@classes/AppError';
import { Prisma as PrismaPG } from '@prisma/postgres';
import { Prisma as PrismaMG } from '@prisma/mongodb';
import { ZodError } from 'zod';

export const errorMiddleware = (_error: unknown, _request: FastifyRequest, reply: FastifyReply) => {
  // console.log('1 _error');
  // console.log(_error);

  let appError: AppError;
  if (_error instanceof AppError) {
    appError = _error as AppError;
  } else if (
    [
      _error instanceof PrismaPG.PrismaClientValidationError,
      _error instanceof PrismaMG.PrismaClientValidationError,
    ].includes(true)
  ) {
    appError = new AppError('GEN006', (_error as PrismaPG.PrismaClientValidationError).stack);
  } else if (
    [
      _error instanceof PrismaPG.PrismaClientKnownRequestError,
      _error instanceof PrismaMG.PrismaClientKnownRequestError,
    ].includes(true)
  ) {
    appError = new AppError('GEN006', (_error as PrismaPG.PrismaClientKnownRequestError).stack);
  } else if (_error instanceof SyntaxError) {
    appError = new AppError('GEN006', _error.stack);
  }
  // For ZodError
  else if ((_error as ZodError)?.issues?.length) {
    appError = new AppError('GEN007', _error as ZodError);
  } else if ((_error as Error)?.stack?.length) {
    appError = new AppError('GEN007', (_error as Error)?.stack);
  } else {
    appError = new AppError('GEN001');
  }
  reply.status(appError.status).send(appError.json);
  return;
};
