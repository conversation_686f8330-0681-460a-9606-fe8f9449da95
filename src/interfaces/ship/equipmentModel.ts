import { DBDataTypeI } from '@consts/common/data';
import { StringNullI } from '@interfaces/common/data';
import type { EquipmentModel } from '@prisma/postgres';
import { UUIDI } from '@schemas/common/common';

export type EquipmentModelNestedClientI = Pick<EquipmentModel, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
export type EquipmentModelShipNestedClientI = {
  id: UUIDI;
  category: EquipmentModelNestedClientI;
  manufacturerName: string;
  model: string;
};
export type EquipmentModelTransformParamsI = {
  equipmentModelId: StringNullI;
  equipmentModelName: StringNullI;
  equipmentModelRawDataId: StringNullI;
  equipmentModelRawDataName: StringNullI;
};
