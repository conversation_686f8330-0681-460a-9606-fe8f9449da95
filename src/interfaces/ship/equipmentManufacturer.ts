import { DBDataTypeI } from '@consts/common/data';
import { StringNullI } from '@interfaces/common/data';
import type { EquipmentManufacturer } from '@prisma/postgres';
import { UUIDI } from '@schemas/common/common';

export type EquipmentManufacturerNestedClientI = Pick<EquipmentManufacturer, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
export type EquipmentManufacturerShipNestedClientI = {
  id: UUIDI;
  category: EquipmentManufacturerNestedClientI;
  manufacturerName: string;
  model: string;
};
export type EquipmentManufacturerTransformParamsI = {
  equipmentManufacturerId: StringNullI;
  equipmentManufacturerName: StringNullI;
  equipmentManufacturerRawDataId: StringNullI;
  equipmentManufacturerRawDataName: StringNullI;
};
