import { DBDataTypeI } from '@consts/common/data';
import { TotalI } from './db';

export type ObjUnknownI = Record<string, unknown>;

export type ObjStrI = Record<string, string>;
export type ObjStrNumI = Record<string, string | number>;
export type PrimitiveI = string | boolean | number;
export type SortOrderI = 'asc' | 'desc';
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequireOne<T, K extends keyof T> = Partial<T> & Pick<T, K>;

export type RequireSome<T, K extends keyof T> = Partial<T> & Pick<T, K>;
export type RequireAndOptional<T, R extends keyof T, O extends keyof T> = Required<Pick<T, R>> &
  Partial<Pick<T, O>> &
  Omit<T, R | O>;

export type NullableAttrI<T, K extends keyof T> = Omit<T, K> & {
  [P in K]: T[P] | null;
};
export type PaginatedDataI<T> = {
  page: number;
  total: number;
  data: T[];
};
export type EitherOr<T, K extends keyof T> = { [P in K]: Required<Pick<T, P>> & Partial<Omit<T, P>> }[K];
export type NullableI<T> = T | null;

export type UndefinedNullableI<T> = T | undefined | null;

export type StringUndefinedI = string | undefined;
export type StringUndefinedNullI = UndefinedNullableI<string>;
export type StringNullI = NullableI<string>;
export type BooleanNullI = NullableI<boolean>;
export type NumberNullI = NullableI<number>;
export type DateNullI = NullableI<Date>;

export type NumberUndefinedNullI = UndefinedNullableI<number>;
export type BooleanUndefinedNullI = UndefinedNullableI<boolean>;
export type KeyStrStrI = Record<string, string>;
export type StrNullVoidFnI = (id: StringNullI) => void;
export type TotalDataI<T> = TotalI & {
  data: T[];
};
export type TotalCursorDataI<T> = TotalDataI<T> & {
  nextCursorId: NumberNullI;
};
export type TotalCursorDateDataI<T> = TotalDataI<T> & {
  nextCursorDate: DateNullI;
};
export type IdNameTypeI = {
  id: string;
  name: string;
  type: DBDataTypeI;
};
export type IdCursorIdI = {
  id: string;
  cursorId: number;
};
