import { ProfileStatusI } from '@consts/user/profile';
import type { Profile, ProfileStatus } from '@prisma/postgres';

export type ProfileStatusResultI = Pick<Profile, 'email' | 'username'> &
  Partial<Pick<Profile, 'name' | 'entityText' | 'designationText' | 'avatar'>> &
  Pick<
    ProfileStatus,
    'isEmailVerified' | 'isPersonalDetailsSaved' | 'isWorkDetailsSaved' | 'isPrivacyPolicyAccepted' | 'profileId'
  > & {
    isUsernameSaved: boolean;
    previousStatus: ProfileStatusI;
  };
