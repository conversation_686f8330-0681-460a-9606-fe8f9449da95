import type { ProfileStatusResultI } from '@interfaces/user/profileStatus';
import type { ProfileStatus } from '@prisma/postgres';

export type AuthRegisterResultI = Pick<ProfileStatus, 'profileId'> & {
  token: string;
};
export type AuthLoginResultI = ProfileStatusResultI & {
  token: string;
  jwtToken: string;
};
export interface SendOTPForPasswordResetI {
  email: string;
}

export interface VerifyOTPForPasswordResetI {
  email: string;
  otp: string;
}

export interface ResetPasswordI {
  email: string;
  newPassword: string;
}
