import type { NullableI, StringNullI } from '@interfaces/common/data';
import type { ProfileForDataI } from '@interfaces/user/profile';

export type QuestionCommentSQLI = {
  id: string;
  cursorId: number;
  text: string;
  isAnonymous: boolean;
  replyCount: number;
  createdAt: Date;
  profileId: string;
  profileName: string;
  profileAvatar: StringNullI;
};

export type QuestionCommentFetchManyReplySQLI = Omit<QuestionCommentSQLI, 'replyCount'>;

export type QuestionCommentFetchManySQLI = NullableI<
  QuestionCommentSQLI & {
    replies: NullableI<QuestionCommentFetchManyReplySQLI[]>;
  }
>;
export type QuestionCommentFetchManyCoreItemI = Omit<
  QuestionCommentSQLI,
  'profileId' | 'profileName' | 'profileAvatar'
>;

export type QuestionCommentFetchManyCoreReplyItemI = Omit<
  QuestionCommentSQLI,
  'replyCount' | 'profileId' | 'profileName' | 'profileAvatar'
> & {
  canDelete: boolean;
  profile: ProfileForDataI;
};

export type QuestionCommentFetchManyResultI = QuestionCommentFetchManyCoreItemI & {
  canDelete: boolean;
  replies: NullableI<QuestionCommentFetchManyCoreReplyItemI[]>;
  profile: ProfileForDataI;
};

export type QuestionCommentEditOneResultI = {
  id: string;
  cursorId: string;
  text: string;
  createdAt: Date;
  updatedAt: Date;
};

export type QuestionCommentDeleteOneI = {
  commentId: string;
};
