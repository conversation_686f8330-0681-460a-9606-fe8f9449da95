import { QuestionTypeI } from "@consts/forum/question";
import { IdNameTypeI } from "@interfaces/common/data";

export type GlobalSearchQuestionItemI = {
  id: string;
  title: string;
  description: string | null;
  type: QuestionTypeI;
  communityId: string;
  communityName: string;
  departmentAlternativeId: string | null;
  departmentName: string | null;
  equipmentCategoryId: string | null;
  equipmentCategoryName: string | null;
  equipmentModelId: string | null;
  equipmentModelName: string | null;
  equipmentManufacturerId: string | null;
  equipmentManufacturerName: string | null;
  topics: IdNameTypeI[] | null;
  answerCount: number;
  upvoteCount: number;
  createdAt: Date;
  profileName: string | null;
  matchedFields: string[];
};

export type GlobalSearchCommunityItemI = {
  id: string;
  name: string;
  description: string | null;
  access: string;
  isRestricted: boolean;
  memberCount: number;
  questionCount: number;
  matchedFields: string[];
};

export type GlobalSearchResponseI<T = unknown> = {
  data: T[];
  total: number;
};

export type GlobalSearchCombinedResponseI = {
  questions?: GlobalSearchResponseI<GlobalSearchQuestionItemI>;
  communities?: GlobalSearchResponseI<GlobalSearchCommunityItemI>;
};
