import type { NullableI, StringNullI } from '@interfaces/common/data';
import type { ProfileForDataI } from '@interfaces/user/profile';

export type AnswerCommentSQLI = {
  id: string;
  cursorId: number;
  text: string;
  replyCount: number;
  createdAt: Date;
  profileId: string;
  profileName: string;
  profileAvatar: StringNullI;
};

export type ForumAnswerCommentFetchManyReplySQLI = Omit<AnswerCommentSQLI, 'replyCount'>;

export type ForumAnswerCommentFetchManySQLI = NullableI<
  AnswerCommentSQLI & {
    replies: NullableI<ForumAnswerCommentFetchManyReplySQLI[]>;
  }
>;
export type ForumAnswerCommentFetchManyCoreItemI = Omit<
  AnswerCommentSQLI,
  'profileId' | 'profileName' | 'profileAvatar'
>;

export type ForumAnswerCommentFetchManyCoreReplyItemI = Omit<
  AnswerCommentSQLI,
  'replyCount' | 'profileId' | 'profileName' | 'profileAvatar'
> & {
  profile: ProfileForDataI;
  canDelete: boolean;
};

export type ForumAnswerCommentFetchManyResultI = ForumAnswerCommentFetchManyCoreItemI & {
  replies: NullableI<ForumAnswerCommentFetchManyCoreReplyItemI[]>;
  profile: ProfileForDataI;
  canDelete: boolean;
};

export type AnswerCommentEditOneResultI = {
  id: string;
  cursorId: string;
  text: string;
  createdAt: Date;
  updatedAt: Date;
};
