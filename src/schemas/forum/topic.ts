import { TopicR } from '@consts/common/regex/regex';
import { PaginationSchema } from '@schemas/common/common';
import { z } from 'zod';

export const TopicFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z
      .string()
      .min(0)
      .max(100)
      .regex(TopicR)
      .transform((data) => data?.trim()?.toLowerCase())
      .optional(),
  }),
);
export const TopicFetchsertForClientSchema = z.object({
  name: z.string().min(2).max(100).regex(TopicR),
});

export type TopicFetchForClientI = z.infer<typeof TopicFetchForClientSchema>;

export const EquipmentCategoryFetchsertSchema = z.object({
  name: z.string().min(2).max(100).regex(TopicR),
});
export type TopicFetchsertI = z.infer<typeof TopicFetchsertForClientSchema>;
