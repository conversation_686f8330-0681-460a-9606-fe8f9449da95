import { CursorPaginationSchema, RouteParamsSchema, UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const QuestionCommentCreateOneSchema = z.object({
  questionId: UUIDSchema,
  text: z.string().min(1).max(255),
  parentCommentId: UUIDSchema.optional(),
});
export type QuestionCommentCreateOneI = z.infer<typeof QuestionCommentCreateOneSchema>;

export const QuestionCommentFetchManySchema = CursorPaginationSchema.extend({
  questionId: UUIDSchema,
});
export type QuestionCommentFetchManyI = z.infer<typeof QuestionCommentFetchManySchema>;

export const QuestionCommentFetchRepliesSchema = CursorPaginationSchema.extend({
  questionId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type QuestionCommentFetchRepliesI = z.infer<typeof QuestionCommentFetchRepliesSchema>;

export const QuestionCommentEditOneSchema = z.object({
  commentId: UUIDSchema,
  newComment: z
    .string()
    .min(1)
    .max(255)
    .refine((val) => val.trim().length > 0),
});
export type QuestionCommentEditOneI = z.infer<typeof QuestionCommentEditOneSchema>;

export const QuestionCommentDeleteOneSchema = RouteParamsSchema;
export type QuestionCommentDeleteOneI = z.infer<typeof QuestionCommentDeleteOneSchema>;
