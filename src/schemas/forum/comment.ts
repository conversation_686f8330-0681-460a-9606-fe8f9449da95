import { CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

const TextSchema = z.string().min(1).max(255);

export const ForumCommentCreateOneSchema = z.object({
  answerId: UUIDSchema,
  text: TextSchema,
  parentCommentId: UUIDSchema.optional(),
});

export type ForumCommentCreateOneI = z.infer<typeof ForumCommentCreateOneSchema>;

export const ForumCommentFetchManySchema = CursorPaginationSchema.extend({
  answerId: UUIDSchema,
});

export type ForumCommentFetchManyI = z.infer<typeof ForumCommentFetchManySchema>;
export const ForumCommentFetchRepliesSchema = CursorPaginationSchema.extend({
  answerId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type ForumCommentFetchRepliesI = z.infer<typeof ForumCommentFetchRepliesSchema>;
