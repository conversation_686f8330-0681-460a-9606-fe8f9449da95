import { MemberTypeE } from '@consts/forum/member';
import { UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const CommunityRequestQuerySchema = z.object({
  memberId: UUIDSchema,
  communityId: UUIDSchema,
});

export const CommunityRequestBodySchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
  requestedType: MemberTypeE,
});

export const CommunityRequestApprovalQuerySchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
});
export const CommunityRequestRejectionQuerySchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
});

export const CommunityRequestRevocationQuerySchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
});

export const CommunityRequestApprovalBodySchema = z.object({
  acceptedType: MemberTypeE,
});

export type CommunityRequestBodyI = z.infer<typeof CommunityRequestBodySchema>;
export type CommunitRequestApprovalQueryI = z.infer<typeof CommunityRequestApprovalQuerySchema>;
export type CommunityRequestRejectionQueryI = z.infer<typeof CommunityRequestRejectionQuerySchema>;
export type CommunityRequestRevocationQueryI = z.infer<typeof CommunityRequestRevocationQuerySchema>;
export type CommunityRequestApprovalBodyI = z.infer<typeof CommunityRequestApprovalBodySchema>;
