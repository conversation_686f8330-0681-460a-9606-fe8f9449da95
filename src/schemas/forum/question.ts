import AppError from '@classes/AppError';
import { DBDataTypeE } from '@consts/common/data';
import { FORUM_MAX_NO_OF_FILES, QuestionTypeE } from '@consts/forum/question';
import { MAX_TOPICS } from '@consts/forum/topic';
import {
  CursorDatePaginationSchema,
  CursorPaginationSchema,
  IdTypeSchema,
  RouteParamsSchema,
  UUIDSchema,
} from '@schemas/common/common';
import { ShipImoClientSchema } from '@schemas/ship/ship';
import z from 'zod';
import { ForumMediaCreateItemSchema } from './common';

export const ForumQuestionFetchManySchema = CursorDatePaginationSchema.extend({
  type: z.enum(['ALL', QuestionTypeE.Values.NORMAL, QuestionTypeE.Values.TROUBLESHOOT]).default('ALL').optional(),
  isLive: z.coerce.boolean().optional().nullable(),
  myRecommended: z.coerce.boolean().optional().nullable(),
  myQuestion: z.coerce.boolean().optional().nullable(),
  myAnswered: z.coerce.boolean().optional().nullable(),
  myCommunity: z.coerce.boolean().optional().nullable(),
  departmentId: UUIDSchema.optional().nullable(),
  departmentDataType: DBDataTypeE.optional().nullable(),
});
export type ForumQuestionFetchManyI = z.infer<typeof ForumQuestionFetchManySchema>;

export const ForumQuestionCreateOneSchema = z
  .object({
    title: z.string().min(1).max(150),
    description: z.string().max(2000).optional().nullable(),
    type: QuestionTypeE,
    communityId: UUIDSchema,
    isLive: z.boolean(),
    equipmentCategory: IdTypeSchema.optional().nullable(),
    equipmentModel: IdTypeSchema.optional().nullable(),
    equipmentManufacturer: IdTypeSchema.optional().nullable(),
    topics: z.array(IdTypeSchema).min(0).max(MAX_TOPICS).optional().nullable(),
    department: IdTypeSchema.nullable(),
    isAnonymous: z.boolean().optional().nullable(),
    files: z.array(ForumMediaCreateItemSchema).max(FORUM_MAX_NO_OF_FILES).optional().nullable(),
    ship: ShipImoClientSchema.optional(),
  })
  .superRefine((data, _ctx) => {
    if (data.type === 'NORMAL') {
      if (!data?.topics?.length) {
        throw new AppError('FMQUE012');
      }
    } else {
      if (
        [
          Boolean(data?.equipmentCategory),
          Boolean(data?.equipmentManufacturer),
          Boolean(data?.equipmentModel),
        ].includes(false)
      ) {
        throw new AppError('FMQUE002');
      }
    }
  });
export type ForumQuestionCreateOneI = z.infer<typeof ForumQuestionCreateOneSchema>;

export const ForumQuestionUpdateOneBodySchema = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().min(1).max(255).optional(),
  communityId: UUIDSchema.optional().nullable(),
  equipmentCategoryId: UUIDSchema.optional().nullable(),
  equipmentCategoryRawDataId: UUIDSchema.optional().nullable(),
  equipmentModelId: UUIDSchema.optional().nullable(),
  equipmentManufacturerId: UUIDSchema.optional().nullable(),
  topicIds: z.array(IdTypeSchema).optional().nullable(),
  topicRawDataIds: z.array(IdTypeSchema).optional().nullable(),
  departmentIds: z.array(IdTypeSchema).optional().nullable(),
  departmentRawDataIds: z.array(IdTypeSchema).optional().nullable(),
  files: z.array(ForumMediaCreateItemSchema).max(FORUM_MAX_NO_OF_FILES).optional(),
});
export type ForumQuestionUpdateOneI = z.infer<typeof ForumQuestionUpdateOneBodySchema>;

export const ForumQuestionUpdateOneQuerySchema = z.object({
  questionId: UUIDSchema,
  communityId: UUIDSchema,
});
export type ForumQuestionUpdateOneQueryI = z.infer<typeof ForumQuestionUpdateOneQuerySchema>;

export const ForumQuestionDeleteOneSchema = RouteParamsSchema;
export type ForumQuestionDeleteOneI = z.infer<typeof ForumQuestionDeleteOneSchema>;

export const ForumQuestionUpdateLiveSchema = z.object({
  questionId: UUIDSchema,
  isLive: z.boolean(),
});
export type ForumQuestionUpdateLiveI = z.infer<typeof ForumQuestionUpdateLiveSchema>;

export const ForumQuestionSearchSchema = CursorPaginationSchema.extend({
  search: z.string().trim().toLowerCase().min(1).max(100),
});
export type ForumQuestionSearchI = z.infer<typeof ForumQuestionSearchSchema>;
