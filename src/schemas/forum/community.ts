import { CommunityAccessE } from '@consts/forum/community';
import { CursorPaginationSchema, FileOperationSchema, RouteParamsSchema } from '@schemas/common/common';
import { z } from 'zod';

export const CommunityCreateOneSchema = z.object({
  name: z.string().min(1).max(100).nullable(),
  access: CommunityAccessE,
  isRestricted: z.boolean(),
  avatar: FileOperationSchema.optional(),
});
export type CommunityCreateOneI = z.infer<typeof CommunityCreateOneSchema>;
export const CommunityUpdateOneSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  access: CommunityAccessE.optional(),
  isRestricted: z.boolean().optional(),
  avatar: FileOperationSchema.optional(),
});

export type CommunityUpdateOneI = z.infer<typeof CommunityUpdateOneSchema>;
export const CommunityFetchForClientSchema = CursorPaginationSchema.extend({
  name: z.string().min(1).max(100).nullable().optional(),
});

export type CommunityFetchForClientI = z.infer<typeof CommunityFetchForClientSchema>;

export const CommunityFetchOneForExternalClientParamsSchema = RouteParamsSchema;
export type CommunityOneForExternalClientParamsI = z.infer<typeof CommunityFetchOneForExternalClientParamsSchema>;
