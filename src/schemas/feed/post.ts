import { POST_MAX_NO_OF_FILES, PostFileExtensionE } from '@consts/feed/post';
import { CursorIdSchema, CursorPaginationSchema, PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { isFilledString } from '@utils/data/string';
import { z } from 'zod';

export const PostCreateItemSchema = z.object({
  caption: z.string().min(1).max(255).nullable(),
  fileUrl: z.string().url(),
  extension: PostFileExtensionE,
});
export type PostCreateItemI = z.infer<typeof PostCreateItemSchema>;

export const PostCreateOneSchema = z
  .object({
    caption: z.string().min(1).max(2000).nullable(),
    files: z.array(PostCreateItemSchema).max(POST_MAX_NO_OF_FILES).optional(),
  })
  .superRefine((data, ctx) => {
    if (!isFilledString(data?.caption) && !data?.files?.length) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Either caption or files or both are mandatory',
      });
    }
  });
export type PostCreateOneI = z.infer<typeof PostCreateOneSchema>;
export const PostFetchManyQuerySchema = CursorPaginationSchema.extend({
  otherCursorId: CursorIdSchema.optional(),
});
export type PostFetchManyQueryI = z.infer<typeof PostFetchManyQuerySchema>;
export const PostFetchManySchema = z.object({
  profileId: UUIDSchema,
  pagination: CursorPaginationSchema,
});
export const ProfilePostFetchManyQuerySchema = CursorPaginationSchema.extend({
  profileId: UUIDSchema,
});
export type ProfilePostFetchManyI = z.infer<typeof PostFetchManyQuerySchema>;
export type PostFetchManyI = z.infer<typeof PostFetchManySchema>;
export const PostUpdateOneSchema = z
  .object({
    postId: UUIDSchema,
    caption: z.string().min(1).max(2000).nullable(),
    files: z.array(PostCreateItemSchema).max(POST_MAX_NO_OF_FILES).optional(),
  })
  .superRefine((data, ctx) => {
    if (!isFilledString(data?.caption) && !data?.files?.length) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Either caption or files or both are mandatory',
      });
    }
  });
export type PostUpdateOneI = z.infer<typeof PostUpdateOneSchema>;
export const PostSearchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(100),
});
export type PostSearchSchemaI = z.infer<typeof PostSearchSchema>;
