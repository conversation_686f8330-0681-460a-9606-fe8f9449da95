import { FileExtensionE, ParentFolderE } from '@consts/storage/storage';
import { z } from 'zod';

export const PresignedURLItemSchema = z.object({
  folder: ParentFolderE,
  extension: FileExtensionE,
});
export type PresignedURLItemI = z.infer<typeof PresignedURLItemSchema>;
export const PresignedURLsSchema = z.object({
  folder: ParentFolderE,
  extensions: z.array(FileExtensionE),
});
export type PresignedURLsI = z.infer<typeof PresignedURLsSchema>;

export const PresignedURLItemResultSchema = z.object({
  extension: FileExtensionE,
  uploadUrl: z.string().url(),
  accessUrl: z.string().url(),
});

export type PresignedURLItemResultI = z.infer<typeof PresignedURLItemResultSchema>;

export const DeleteFileSchema = z.object({
  fileUrl: z.string().url(),
});
