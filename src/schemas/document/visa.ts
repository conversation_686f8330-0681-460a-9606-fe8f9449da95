import { VisaNameR } from '@consts/common/regex/regex';
import { DocumentOprTypeE, OprTypeE } from '@consts/common/data';
import { CountryIso2Schema } from '@schemas/common/common';
import { z } from 'zod';

export const VisaPostBodySchema = z
  .object({
    documentNo: z.string().min(3).max(50),
    file: z
      .object({
        opr: DocumentOprTypeE,
        fileUrl: z.string().url().optional(),
      })
      .optional()
      .nullable(),
    name: z.string().min(2).max(150).regex(VisaNameR),
    countryIso2: CountryIso2Schema,
    fromDate: z.coerce.date(),
    untilDate: z
      .string()
      .transform((data) => new Date(data))
      .nullable(),
  })
  .superRefine((data, ctx) => {
    if (data?.untilDate && data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than To date",
        path: ['fromDate'],
      });
    }
  });

export type VisaPostBodyI = z.infer<typeof VisaPostBodySchema>;

export const VisaPatchBodySchema = z
  .object({
    name: z.string().min(2).max(150).regex(VisaNameR).optional(),
    documentNo: z.string().min(3).max(50).optional(),
    countryIso2: CountryIso2Schema.optional(),
    fromDate: z.coerce.date().optional(),
    untilDate: z.coerce.date().optional().nullable(),
    file: z
      .object({
        opr: OprTypeE,
        fileUrl: z.string().url().optional().nullable(),
      })
      .optional(),
  })
  .superRefine((data, ctx) => {
    if (!Object.keys(data).some((key) => data[key])) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'At least one attribute is required',
      });
    } else if (data.fromDate && data.untilDate && data.untilDate instanceof Date && data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than Until date",
        path: ['fromDate'],
      });
    }

    if (data?.file?.opr && (data.file.opr === 'CREATE' || data.file.opr === 'UPDATE')) {
      if (!data?.file?.fileUrl) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'fileUrl is required',
          path: ['file', 'fileUrl'],
        });
      }
    }

    if (data?.file?.opr === 'DELETE') {
      if (data.file.fileUrl !== undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'DELETE operation does not allow fileUrl field',
          path: ['file', 'fileUrl'],
        });
      }
    }
  });

export type VisaPatchBodyI = z.infer<typeof VisaPatchBodySchema>;
