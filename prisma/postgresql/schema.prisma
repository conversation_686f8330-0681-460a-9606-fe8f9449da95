generator client {
  provider        = "prisma-client-js"
  output          = "../../node_modules/@prisma/postgres"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_DATABASE_URL")
  schemas  = ["auth", "app", "career", "company", "document", "feed", "forum", "master", "network", "port", "rawData", "score", "ship", "user"]
}

//#region app
enum PlatformE {
  android
  ios
  web_app

  @@schema("app")
}

enum PolicyTypeE {
  SIGNUP_PRIVACY_POLICY
  TERMS_OF_USE

  @@schema("app")
}

model Platform {
  id        PlatformE
  url       String    @db.VarChar(255)
  createdAt DateTime  @default(now()) @db.Timestamp()
  updatedAt DateTime  @updatedAt @db.Timestamp()
  Session   Session[]

  @@id([id])
  @@schema("app")
}

model AppVersion {
  id         String    @default(uuid()) @db.Uuid
  platformId PlatformE
  versionNo  String    @db.VarChar(15)
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now()) @db.Timestamp()
  updatedAt  DateTime  @updatedAt @db.Timestamp()
  Session    Session[]

  @@id([id])
  @@unique([platformId, versionNo])
  @@schema("app")
}

model PrivacyPolicy {
  id        String       @default(uuid()) @db.Uuid
  content   String       @db.Text
  type      PolicyTypeE?
  isActive  Boolean
  createdAt DateTime     @default(now()) @db.Timestamptz()
  updatedAt DateTime     @updatedAt @db.Timestamptz()

  @@id([id])
  @@schema("app")
}

//#endregion app

//#region auth
model Session {
  id           String    @default(uuid()) @db.Uuid
  profileId    String    @db.Uuid
  ipAddress    String    @db.VarChar(39)
  platformId   PlatformE
  appVersionId String    @db.Uuid
  deviceId     String    @db.Uuid
  deviceToken  String?   @db.Text
  expiryDate   DateTime
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now()) @db.Timestamp()
  lastActivity DateTime  @updatedAt @db.Timestamp()

  Platform   Platform   @relation(fields: [platformId], references: [id])
  AppVersion AppVersion @relation(fields: [appVersionId], references: [id])

  @@id([id])
  @@schema("auth")
}

//#endregion auth
//#region career
model ProfileEducation {
  id              String         @id @default(uuid()) @db.Uuid
  profileId       String         @db.Uuid
  entityId        String?        @db.Uuid
  entityRawDataId String?        @db.Uuid
  degreeId        String?        @db.Uuid
  degreeRawDataId String?        @db.Uuid
  fromDate        DateTime       @db.Date
  toDate          DateTime?      @db.Date
  createdAt       DateTime       @default(now()) @db.Timestamp()
  updatedAt       DateTime       @updatedAt @db.Timestamp()
  Degree          Degree?        @relation(fields: [degreeId], references: [id])
  DegreeRawData   DegreeRawData? @relation(fields: [degreeRawDataId], references: [id])
  Entity          Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData   EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Profile         Profile        @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ProfileSkill {
  id             String        @id @default(uuid()) @db.Uuid
  skillId        String?       @db.Uuid
  skillRawDataId String?       @db.Uuid
  profileId      String        @db.Uuid
  createdAt      DateTime      @default(now()) @db.Timestamp()
  updatedAt      DateTime      @updatedAt @db.Timestamp()
  Skill          Skill?        @relation(fields: [skillId], references: [id], onDelete: Cascade)
  SkillRawData   SkillRawData? @relation(fields: [skillRawDataId], references: [id], onDelete: Cascade)
  Profile        Profile       @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ProfileSkillEntityDegree {
  id              String         @id @default(uuid()) @db.Uuid
  skillId         String?        @db.Uuid
  skillRawDataId  String?        @db.Uuid
  entityId        String?        @db.Uuid
  entityRawDataId String?        @db.Uuid
  degreeId        String?        @db.Uuid
  degreeRawDataId String?        @db.Uuid
  profileId       String         @db.Uuid
  createdAt       DateTime       @default(now()) @db.Timestamp()
  updatedAt       DateTime       @updatedAt @db.Timestamp()
  Degree          Degree?        @relation(fields: [degreeId], references: [id])
  DegreeRawData   DegreeRawData? @relation(fields: [degreeRawDataId], references: [id])
  Entity          Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData   EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Skill           Skill?         @relation(fields: [skillId], references: [id])
  SkillRawData    SkillRawData?  @relation(fields: [skillRawDataId], references: [id])

  @@schema("career")
}

model ProfileSkillEntityCertificate {
  id                   String         @id @default(uuid()) @db.Uuid
  skillId              String?        @db.Uuid
  skillRawDataId       String?        @db.Uuid
  entityId             String?        @db.Uuid
  entityRawDataId      String?        @db.Uuid
  certificateId        String?        @db.Uuid
  certificateRawDataId String?        @db.Uuid
  profileId            String         @db.Uuid
  createdAt            DateTime       @default(now()) @db.Timestamp()
  updatedAt            DateTime       @updatedAt @db.Timestamp()
  Entity               Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData        EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Skill                Skill?         @relation(fields: [skillId], references: [id])
  SkillRawData         SkillRawData?  @relation(fields: [skillRawDataId], references: [id])
  Profile              Profile        @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ProfileSkillExperienceShip {
  id               String   @id @default(uuid()) @db.Uuid
  skillId          String?  @db.Uuid
  skillRawDataId   String?  @db.Uuid
  experienceShipId String   @db.Uuid
  profileId        String   @db.Uuid
  createdAt        DateTime @default(now()) @db.Timestamp()
  updatedAt        DateTime @updatedAt @db.Timestamp()

  Skill        Skill?        @relation(fields: [skillId], references: [id])
  SkillRawData SkillRawData? @relation(fields: [skillRawDataId], references: [id])

  ExperienceShip ExperienceShip? @relation(fields: [experienceShipId], references: [id])

  @@schema("career")
}

model ProfileCertificate {
  id                         String                    @id @default(uuid()) @db.Uuid
  profileId                  String                    @db.Uuid
  entityId                   String?                   @db.Uuid
  entityRawDataId            String?                   @db.Uuid
  certificateCourseId        String?                   @db.Uuid
  certificateCourseRawDataId String?                   @db.Uuid
  fromDate                   DateTime                  @db.Date
  untilDate                  DateTime?                 @db.Date
  fileUrl                    String?                   @db.Text
  createdAt                  DateTime                  @default(now()) @db.Timestamp()
  updatedAt                  DateTime                  @updatedAt @db.Timestamp()
  CertificateCourse          CertificateCourse?        @relation(fields: [certificateCourseId], references: [id])
  CertificateCourseRawData   CertificateCourseRawData? @relation(fields: [certificateCourseRawDataId], references: [id])
  Entity                     Entity?                   @relation(fields: [entityId], references: [id])
  EntityRawData              EntityRawData?            @relation(fields: [entityRawDataId], references: [id])
  Profile                    Profile                   @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model Experience {
  id                    String                  @id @default(uuid()) @db.Uuid
  profileId             String                  @db.Uuid
  entityId              String?                 @db.Uuid
  entityRawDataId       String?                 @db.Uuid
  years                 Int                     @default(0)
  months                Int                     @default(0)
  Entity                Entity?                 @relation(fields: [entityId], references: [id])
  EntityRawData         EntityRawData?          @relation(fields: [entityRawDataId], references: [id])
  Profile               Profile                 @relation(fields: [profileId], references: [id])
  ExperienceDesignation ExperienceDesignation[]

  @@schema("career")
}

model ExperienceDesignation {
  id                       String                  @id @default(uuid()) @db.Uuid
  profileId                String                  @db.Uuid
  experienceId             String                  @db.Uuid
  designationAlternativeId String?                 @db.Uuid
  designationRawDataId     String?                 @db.Uuid
  fromDate                 DateTime                @db.Date
  toDate                   DateTime?               @db.Date
  createdAt                DateTime                @default(now()) @db.Timestamptz()
  updatedAt                DateTime                @updatedAt @db.Timestamptz()
  DesignationAlternative   DesignationAlternative? @relation(fields: [designationAlternativeId], references: [id])
  DesignationRawData       DesignationRawData?     @relation(fields: [designationRawDataId], references: [id])
  Experience               Experience              @relation(fields: [experienceId], references: [id], onDelete: Cascade)
  ExperienceShip           ExperienceShip[]
  Profile                  Profile                 @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ExperienceShip {
  id                          String                        @id @default(uuid()) @db.Uuid
  profileId                   String                        @db.Uuid
  experienceDesignationId     String                        @db.Uuid
  shipImo                     String?                       @db.VarChar(7)
  shipRawDataImo              String?                       @db.VarChar(7)
  name                        String                        @db.VarChar(100)
  subVesselTypeId             String?                       @db.Uuid
  subVesselTypeRawDataId      String?                       @db.Uuid
  sizeGt                      Decimal                       @db.Decimal(10, 2)
  powerKw                     Decimal?                      @db.Decimal(8, 2)
  dwt                         Int?                          @db.Integer
  details                     String?                       @db.VarChar(255)
  fromDate                    DateTime                      @db.Date
  toDate                      DateTime?                     @db.Date
  departmentAlternativeId     String?                       @db.Uuid
  departmentRawDataId         String?                       @db.Uuid
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ExperienceCargo             ExperienceCargo[]
  ExperienceEquipmentCategory ExperienceEquipmentCategory[]
  DepartmentAlternative       DepartmentAlternative?        @relation(fields: [departmentAlternativeId], references: [id])
  DepartmentRawData           DepartmentRawData?            @relation(fields: [departmentRawDataId], references: [id])
  ExperienceDesignation       ExperienceDesignation         @relation(fields: [experienceDesignationId], references: [id], onDelete: Cascade)
  Ship                        Ship?                         @relation(fields: [shipImo], references: [imo])
  ShipRawData                 ShipRawData?                  @relation(fields: [shipRawDataImo], references: [imo])
  SubVesselType               SubVesselType?                @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData        SubVesselTypeRawData?         @relation(fields: [subVesselTypeRawDataId], references: [id])
  Profile                     Profile                       @relation(fields: [profileId], references: [id])
  ProfileSkillExperienceShip  ProfileSkillExperienceShip[]

  @@schema("career")
}

model ExperienceEquipmentCategory {
  id                         String                    @id @default(uuid()) @db.Uuid
  experienceShipId           String                    @db.Uuid
  equipmentCategoryId        String?                   @db.Uuid
  equipmentCategoryRawDataId String?                   @db.Uuid
  manufacturerName           String                    @db.VarChar(100)
  model                      String                    @db.VarChar(100)
  powerCapacity              Decimal?                  @db.Decimal(8, 2)
  details                    String?                   @db.VarChar(150)
  createdAt                  DateTime                  @default(now()) @db.Timestamptz()
  updatedAt                  DateTime                  @updatedAt @db.Timestamptz()
  EquipmentCategory          EquipmentCategory?        @relation(fields: [equipmentCategoryId], references: [id])
  EquipmentCategoryRawData   EquipmentCategoryRawData? @relation(fields: [equipmentCategoryRawDataId], references: [id])
  ExperienceShip             ExperienceShip            @relation(fields: [experienceShipId], references: [id], onDelete: Cascade)
  ExperienceFuelType         ExperienceFuelType[]

  @@schema("career")
}

model ExperienceFuelType {
  id                            String                      @id @default(uuid()) @db.Uuid
  experienceEquipmentCategoryId String                      @db.Uuid
  fuelTypeId                    String?                     @db.Uuid
  fuelTypeRawDataId             String?                     @db.Uuid
  ExperienceEquipmentCategory   ExperienceEquipmentCategory @relation(fields: [experienceEquipmentCategoryId], references: [id], onDelete: Cascade)
  FuelType                      FuelType?                   @relation(fields: [fuelTypeId], references: [id])
  FuelTypeRawData               FuelTypeRawData?            @relation(fields: [fuelTypeRawDataId], references: [id])

  @@schema("career")
}

model ExperienceCargo {
  id               String         @id @default(uuid()) @db.Uuid
  experienceShipId String         @db.Uuid
  name             String         @db.VarChar(100)
  description      String?        @db.VarChar(150)
  fromDate         DateTime       @db.Date
  toDate           DateTime?      @db.Date
  createdAt        DateTime       @default(now()) @db.Timestamptz()
  updatedAt        DateTime       @updatedAt @db.Timestamptz()
  ExperienceShip   ExperienceShip @relation(fields: [experienceShipId], references: [id], onDelete: Cascade)

  @@schema("career")
}

//#endregion career
//#region company
model Entity {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(255)
  countryIso2                   String?                         @db.Char(2)
  website                       String?                         @db.VarChar(255)
  type                          EntityTypeE
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  Country                       Country?                        @relation(fields: [countryIso2], references: [iso2])
  Experience                    Experience[]
  ProfileCertificate            ProfileCertificate[]
  ProfileEducation              ProfileEducation[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  Profile                       Profile[]
  EntitySubTypeEntity           EntitySubTypeEntity[]

  @@index([name])
  @@schema("company")
}

model EntitySubType {
  id                  String                @id @default(uuid()) @db.Uuid
  name                String                @db.VarChar(100)
  type                EntityTypeE
  createdAt           DateTime              @default(now()) @db.Timestamptz()
  updatedAt           DateTime              @updatedAt @db.Timestamptz()
  EntitySubTypeEntity EntitySubTypeEntity[]

  @@schema("company")
}

model EntitySubTypeEntity {
  entityId        String        @db.Uuid
  subEntityTypeId String        @db.Uuid
  createdAt       DateTime      @default(now()) @db.Timestamptz()
  updatedAt       DateTime      @updatedAt @db.Timestamptz()
  Entity          Entity        @relation(fields: [entityId], references: [id])
  EntitySubType   EntitySubType @relation(fields: [subEntityTypeId], references: [id])

  @@unique([entityId, subEntityTypeId])
  @@schema("company")
}

model Department {
  id                    String                  @id @default(uuid()) @db.Uuid
  name                  String                  @db.VarChar(100)
  createdAt             DateTime                @default(now()) @db.Timestamp()
  updatedAt             DateTime                @updatedAt @db.Timestamp()
  DepartmentAlternative DepartmentAlternative[]

  @@schema("company")
}

model DepartmentAlternative {
  id             String           @id @default(uuid()) @db.Uuid
  name           String           @db.VarChar(100)
  departmentId   String           @db.Uuid
  createdAt      DateTime         @default(now()) @db.Timestamp()
  updatedAt      DateTime         @updatedAt @db.Timestamp()
  ExperienceShip ExperienceShip[]
  Department     Department       @relation(fields: [departmentId], references: [id])
  Question       Question[]

  @@schema("company")
}

model Designation {
  id                     String                   @id @default(uuid()) @db.Uuid
  name                   String                   @db.VarChar(100)
  createdAt              DateTime                 @default(now()) @db.Timestamp()
  updatedAt              DateTime                 @updatedAt @db.Timestamp()
  DesignationAlternative DesignationAlternative[]

  @@schema("company")
}

model DesignationAlternative {
  id                    String                  @id @default(uuid()) @db.Uuid
  name                  String                  @db.VarChar(100)
  designationId         String                  @db.Uuid
  createdAt             DateTime                @default(now()) @db.Timestamp()
  updatedAt             DateTime                @updatedAt @db.Timestamp()
  ExperienceDesignation ExperienceDesignation[]
  Designation           Designation             @relation(fields: [designationId], references: [id], onDelete: Cascade)
  Profile               Profile[]

  @@schema("company")
}

model Skill {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(100)
  category                      SkillCategoryE
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  ProfileSkill                  ProfileSkill[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  ProfileSkillExperienceShip    ProfileSkillExperienceShip[]

  @@schema("company")
}

model Degree {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(150)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  ProfileEducation         ProfileEducation[]
  ProfileSkillEntityDegree ProfileSkillEntityDegree[]

  @@schema("company")
}

model CertificateCourse {
  id                 String                 @id @default(uuid()) @db.Uuid
  name               String                 @db.VarChar(255)
  type               CertificateCourseTypeE
  createdAt          DateTime               @default(now()) @db.Timestamptz()
  updatedAt          DateTime               @updatedAt @db.Timestamptz()
  ProfileCertificate ProfileCertificate[]

  @@schema("company")
}

//#endregion company

//#region document
model Identity {
  id          String        @id @default(uuid()) @db.Uuid
  documentNo  String        @db.VarChar(50)
  profileId   String        @db.Uuid
  fileUrl     String?       @db.Text
  type        IdentityTypeE
  orderIndex  Int
  countryIso2 String        @db.Char(2)
  fromDate    DateTime      @db.Date
  untilDate   DateTime?     @db.Date
  createdAt   DateTime      @default(now()) @db.Timestamp()
  updatedAt   DateTime      @updatedAt @db.Timestamp()
  Country     Country       @relation(fields: [countryIso2], references: [iso2])
  Profile     Profile       @relation(fields: [profileId], references: [id])

  @@index([profileId, type])
  @@index([profileId, documentNo])
  @@schema("document")
}

model Visa {
  id          String    @id @default(uuid()) @db.Uuid
  documentNo  String    @db.VarChar(50)
  name        String?   @db.VarChar(150)
  profileId   String    @db.Uuid
  fileUrl     String?   @db.Text
  countryIso2 String    @db.Char(2)
  fromDate    DateTime  @db.Date
  untilDate   DateTime? @db.Date
  createdAt   DateTime  @default(now()) @db.Timestamp()
  updatedAt   DateTime  @updatedAt @db.Timestamp()
  Country     Country   @relation(fields: [countryIso2], references: [iso2])
  profile     Profile   @relation(fields: [profileId], references: [id])

  @@unique([profileId, documentNo])
  @@schema("document")
}

//#endregion document

//#region feed
model Post {
  id                  String         @id @default(uuid()) @db.Uuid
  cursorId            BigInt         @default(autoincrement()) @db.BigInt()
  caption             String?        @db.VarChar(2000)
  reactionsCount      Int            @default(0)
  parentCommentsCount Int            @default(0)
  totalCommentsCount  Int            @default(0)
  profileId           String         @db.Uuid
  status              PostStatusE    @default(ACTIVE)
  createdAt           DateTime       @default(now()) @db.Timestamp()
  updatedAt           DateTime       @updatedAt @db.Timestamp()
  Profile             Profile        @relation(fields: [profileId], references: [id])
  Comments            PostComment[]
  Media               PostMedia[]
  Reactions           PostReaction[]

  @@index([profileId])
  @@index([cursorId])
  @@schema("feed")
}

enum PostFileExtensionE {
  webp
  jpeg
  jpg

  @@schema("feed")
}

model PostMedia {
  id            String             @id @default(uuid()) @db.Uuid
  postId        String             @db.Uuid
  profileId     String             @db.Uuid
  caption       String?            @db.VarChar(1000)
  fileUrl       String             @db.Text
  fileExtension PostFileExtensionE
  createdAt     DateTime           @default(now()) @db.Timestamp()
  updatedAt     DateTime           @updatedAt @db.Timestamp()
  Post          Post               @relation(fields: [postId], references: [id], onDelete: Cascade)
  Profile       Profile            @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@index([profileId])
  @@schema("feed")
}

model PostReaction {
  profileId    String        @db.Uuid
  postId       String        @db.Uuid
  reactionType ReactionTypeE @default(LIKE)
  createdAt    DateTime      @default(now()) @db.Timestamp()
  Post         Post          @relation(fields: [postId], references: [id], onDelete: Cascade)
  Profile      Profile       @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@id([profileId, postId])
  @@index([postId])
  @@schema("feed")
}

model PostComment {
  id              String                @default(uuid()) @db.Uuid
  cursorId        BigInt                @default(autoincrement()) @db.BigInt()
  text            String                @db.VarChar(255)
  profileId       String                @db.Uuid
  postId          String                @db.Uuid
  status          PostStatusE           @default(ACTIVE)
  repliesCount    Int?
  reactionsCount  Int                   @default(0)
  parentCommentId String?               @db.Uuid
  createdAt       DateTime              @default(now()) @db.Timestamp()
  updatedAt       DateTime              @updatedAt @db.Timestamp()
  Parent          PostComment?          @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  Replies         PostComment[]         @relation("CommentReplies")
  Post            Post                  @relation(fields: [postId], references: [id], onDelete: Cascade)
  Profile         Profile               @relation(fields: [profileId], references: [id], onDelete: Cascade)
  Reactions       PostCommentReaction[]

  @@id(id)
  @@index([postId])
  @@index([profileId])
  @@index([parentCommentId])
  @@schema("feed")
}

model PostCommentReaction {
  profileId    String        @db.Uuid
  commentId    String        @db.Uuid
  reactionType ReactionTypeE @default(LIKE)
  createdAt    DateTime      @default(now()) @db.Timestamp()
  Comment      PostComment   @relation(fields: [commentId], references: [id], onDelete: Cascade)
  Profile      Profile       @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@id([profileId, commentId, reactionType])
  @@index([commentId])
  @@schema("feed")
}

//#endregion feed

//#region forum
model Community {
  id            String           @default(uuid()) @db.Uuid
  cursorId      BigInt           @default(autoincrement()) @db.BigInt()
  name          String           @db.VarChar(100)
  memberCount   Int              @default(0)
  questionCount Int              @default(0)
  access        CommunityAccessE
  creatorId     String?          @db.Uuid
  isRestricted  Boolean          @default(false)
  createdAt     DateTime         @default(now()) @db.Timestamptz()
  updatedAt     DateTime         @updatedAt @db.Timestamptz()
  avatar        String?          @db.Text

  CommunityMember  CommunityMember[]
  Profile          Profile?           @relation(fields: [creatorId], references: [id])
  Question         Question[]
  Answer           Answer[]
  CommunityRequest CommunityRequest[]
  AnswerVote       AnswerVote[]
  QuestionVote     QuestionVote[]
  AnswerComment    AnswerComment[]
  QuestionMedia    QuestionMedia[]
  AnswerMedia      AnswerMedia[]

  @@id([id])
  @@schema("forum")
}

model CommunityMember {
  communityId String      @db.Uuid
  profileId   String      @db.Uuid
  type        MemberTypeE @default(MEMBER)

  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  Community Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile   Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@id([communityId, profileId])
  @@unique([communityId, profileId])
  @@schema("forum")
}

model CommunityRequest {
  communityId   String                  @db.Uuid
  profileId     String                  @db.Uuid
  requestedType MemberTypeE
  acceptedType  MemberTypeE?
  status        CommunityRequestStatusE @default(PENDING)
  createdAt     DateTime                @default(now()) @db.Timestamptz()
  updatedAt     DateTime                @updatedAt @db.Timestamptz()

  Community Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile   Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@id([communityId, profileId])
  @@unique([communityId, profileId])
  @@schema("forum")
}

model Question {
  id                             String        @default(uuid()) @db.Uuid
  cursorId                       BigInt        @default(autoincrement()) @db.BigInt()
  title                          String        @db.VarChar(150)
  description                    String        @db.VarChar(2000)
  type                           QuestionTypeE @default(NORMAL)
  communityId                    String        @db.Uuid
  profileId                      String        @db.Uuid
  upvoteCount                    Int           @default(0)
  downvoteCount                  Int           @default(0)
  answerCount                    Int           @default(0)
  commentCount                   Int           @default(0)
  isLive                         Boolean
  liveStartedAt                  DateTime?     @db.Timestamptz()
  shipImo                        String?       @db.VarChar(7)
  shipRawDataImo                 String?       @db.VarChar(7)
  mainVesselTypeId               String?       @db.Uuid
  mainVesselTypeRawDataId        String?       @db.Uuid
  subVesselTypeId                String?       @db.Uuid
  subVesselTypeRawDataId         String?       @db.Uuid
  isSolved                       Boolean       @default(false)
  isEdited                       Boolean       @default(false)
  isAnonymous                    Boolean       @default(false)
  equipmentCategoryId            String?       @db.Uuid
  equipmentCategoryRawDataId     String?       @db.Uuid
  equipmentModelId               String?       @db.Uuid
  equipmentModelRawDataId        String?       @db.Uuid
  equipmentManufacturerId        String?       @db.Uuid
  equipmentManufacturerRawDataId String?       @db.Uuid
  departmentAlternativeId        String?       @db.Uuid
  departmentRawDataId            String?       @db.Uuid
  createdAt                      DateTime      @default(now()) @db.Timestamptz()
  updatedAt                      DateTime      @updatedAt @db.Timestamptz()

  Community                    Community                     @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile                      Profile                       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EquipmentCategory            EquipmentCategory?            @relation(fields: [equipmentCategoryId], references: [id])
  EquipmentCategoryRawData     EquipmentCategoryRawData?     @relation(fields: [equipmentCategoryRawDataId], references: [id])
  EquipmentModel               EquipmentModel?               @relation(fields: [equipmentModelId], references: [id])
  EquipmentModelRawData        EquipmentModelRawData?        @relation(fields: [equipmentModelRawDataId], references: [id])
  EquipmentManufacturer        EquipmentManufacturer?        @relation(fields: [equipmentManufacturerId], references: [id])
  EquipmentManufacturerRawData EquipmentManufacturerRawData? @relation(fields: [equipmentManufacturerRawDataId], references: [id])

  Ship        Ship?        @relation(fields: [shipImo], references: [imo])
  ShipRawData ShipRawData? @relation(fields: [shipRawDataImo], references: [imo])

  MainVesselType        MainVesselType?        @relation(fields: [mainVesselTypeId], references: [id])
  MainVesselTypeRawData MainVesselTypeRawData? @relation(fields: [mainVesselTypeRawDataId], references: [id])

  SubVesselType        SubVesselType?        @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData SubVesselTypeRawData? @relation(fields: [subVesselTypeRawDataId], references: [id])

  DepartmentAlternative DepartmentAlternative? @relation(fields: [departmentAlternativeId], references: [id], onDelete: Cascade)
  DepartmentRawData     DepartmentRawData?     @relation(fields: [departmentRawDataId], references: [id], onDelete: Cascade)

  Answer          Answer[]
  QuestionVote    QuestionVote[]
  QuestionMedia   QuestionMedia[]
  QuestionTopic   QuestionTopic[]
  QuestionComment QuestionComment[]
  AnswerVote      AnswerVote[]
  AnswerComment   AnswerComment[]

  @@id([id])
  @@schema("forum")
}

model QuestionMedia {
  id          String @default(uuid()) @db.Uuid
  questionId  String @db.Uuid
  fileUrl     String @db.Text
  communityId String @db.Uuid

  fileExtension ForumFileExtensionE
  Community     Community           @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Question      Question            @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

model Answer {
  id            String        @default(uuid()) @db.Uuid
  cursorId      BigInt        @default(autoincrement()) @db.BigInt()
  text          String        @db.VarChar(1000)
  upvoteCount   Int           @default(0)
  downvoteCount Int           @default(0)
  commentCount  Int           @default(0)
  questionId    String        @db.Uuid
  communityId   String        @db.Uuid
  profileId     String        @db.Uuid
  status        AnswerStatusE @default(PROPOSED_SOLUTION)
  isEdited      Boolean       @default(false)
  validatedBy   String?       @db.Uuid
  createdAt     DateTime      @default(now()) @db.Timestamptz()
  updatedAt     DateTime      @updatedAt @db.Timestamptz()

  Community   Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile     Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  ValidatedBy Profile?  @relation("ValidatedBy", fields: [validatedBy], references: [id], onDelete: Cascade)
  Question    Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)

  Vote          AnswerVote[]
  AnswerComment AnswerComment[]
  AnswerMedia   AnswerMedia[]

  @@id([id])
  @@schema("forum")
}

model AnswerMedia {
  id          String @default(uuid()) @db.Uuid
  answerId    String @db.Uuid
  fileUrl     String @db.Text
  communityId String @db.Uuid

  fileExtension ForumFileExtensionE
  Community     Community           @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Answer        Answer              @relation(fields: [answerId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

model AnswerVote {
  id          String    @default(uuid()) @db.Uuid
  cursorId    BigInt    @default(autoincrement()) @db.BigInt()
  answerId    String    @db.Uuid
  communityId String    @db.Uuid
  questionId  String    @db.Uuid
  profileId   String    @db.Uuid
  type        VoteTypeE
  createdAt   DateTime  @default(now()) @db.Timestamptz()
  updatedAt   DateTime  @updatedAt @db.Timestamptz()

  Answer    Answer    @relation(fields: [answerId], references: [id], onDelete: Cascade)
  Community Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile   Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  Question  Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@id([id])
  @@unique([answerId, profileId])
  @@schema("forum")
}

model QuestionVote {
  id          String    @default(uuid()) @db.Uuid
  cursorId    BigInt    @default(autoincrement()) @db.BigInt()
  questionId  String    @db.Uuid
  communityId String    @db.Uuid
  profileId   String    @db.Uuid
  type        VoteTypeE
  createdAt   DateTime  @default(now()) @db.Timestamptz()
  updatedAt   DateTime  @updatedAt @db.Timestamptz()

  Question  Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)
  Community Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile   Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@id([id])
  @@unique([questionId, profileId]) // A profile can only vote once per question
  @@schema("forum")
}

model Topic {
  id            String          @default(uuid()) @db.Uuid
  name          String          @db.VarChar(100)
  count         Int             @default(0)
  createdAt     DateTime        @default(now()) @db.Timestamptz()
  updatedAt     DateTime        @updatedAt @db.Timestamptz()
  QuestionTopic QuestionTopic[]

  @@id([id])
  @@schema("forum")
}

model QuestionTopic {
  id             String   @default(uuid()) @db.Uuid
  cursorId       BigInt   @default(autoincrement()) @db.BigInt()
  communityId    String   @db.Uuid
  questionId     String   @db.Uuid
  topicId        String?  @db.Uuid
  topicRawDataId String?  @db.Uuid
  createdAt      DateTime @default(now()) @db.Timestamptz()
  updatedAt      DateTime @updatedAt @db.Timestamptz()

  Question     Question      @relation(fields: [questionId], references: [id], onDelete: Cascade)
  Topic        Topic?        @relation(fields: [topicId], references: [id], onDelete: Cascade)
  TopicRawData TopicRawData? @relation(fields: [topicRawDataId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

model QuestionComment {
  id              String            @default(uuid()) @db.Uuid
  cursorId        BigInt            @default(autoincrement()) @db.BigInt()
  questionId      String            @db.Uuid
  profileId       String            @db.Uuid
  text            String            @db.VarChar(255)
  parentCommentId String?           @db.Uuid
  replyCount      Int?
  isAnonymous     Boolean           @default(false)
  createdAt       DateTime          @default(now()) @db.Timestamp()
  updatedAt       DateTime          @updatedAt @db.Timestamptz()
  Question        Question          @relation(fields: [questionId], references: [id], onDelete: Cascade)
  Profile         Profile           @relation(fields: [profileId], references: [id], onDelete: Cascade)
  Parent          QuestionComment?  @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  replies         QuestionComment[] @relation("CommentReplies")

  @@id([id])
  @@schema("forum")
}

model AnswerComment {
  id              String   @default(uuid()) @db.Uuid
  cursorId        BigInt   @default(autoincrement()) @db.BigInt()
  communityId     String   @db.Uuid
  questionId      String   @db.Uuid
  answerId        String   @db.Uuid
  parentCommentId String?  @db.Uuid
  profileId       String   @db.Uuid
  replyCount      Int?
  text            String   @db.VarChar(255)
  createdAt       DateTime @default(now()) @db.Timestamptz()
  updatedAt       DateTime @updatedAt @db.Timestamptz()

  Answer    Answer    @relation(fields: [answerId], references: [id], onDelete: Cascade)
  Community Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile   Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  Question  Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)

  Parent AnswerComment? @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)

  Replies AnswerComment[] @relation("CommentReplies")

  @@id([id])
  @@schema("forum")
}

enum CommunityAccessE {
  GLOBAL
  PUBLIC
  PRIVATE

  @@schema("forum")
}

enum ForumFileExtensionE {
  webp
  jpeg
  jpg
  pdf
  xls
  xlsx

  @@schema("forum")
}

enum AnswerStatusE {
  PROPOSED_SOLUTION
  VERIFIED_SOLUTION

  @@schema("forum")
}

enum VoteTypeE {
  UPVOTE
  DOWNVOTE

  @@schema("forum")
}

enum QuestionTypeE {
  NORMAL
  TROUBLESHOOT

  @@schema("forum")
}

enum MemberTypeE {
  ADMIN
  MODERATOR
  CONTRIBUTOR
  MEMBER

  @@schema("forum")
}

enum CommunityRequestStatusE {
  PENDING
  ACCEPTED
  PARTIALLY_ACCEPTED
  REJECTED
  REVOKED

  @@schema("forum")
}

//#endregion forum
//#region master
model City {
  id          String        @id @db.Uuid
  geoNameId   String?       @db.VarChar(15)
  name        String        @db.VarChar(50)
  countryIso2 String        @db.Char(2)
  createdAt   DateTime      @default(now()) @db.Timestamptz()
  updatedAt   DateTime      @updatedAt @db.Timestamptz()
  Country     Country       @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Port        Port[]
  PortRawData PortRawData[]

  @@index([name])
  @@schema("master")
}

model Country {
  iso2      String   @id @unique @db.Char(2)
  iso3      String   @unique @db.Char(3)
  name      String   @db.VarChar(50)
  native    String?  @db.VarChar(100)
  continent String?  @db.VarChar(50)
  capital   String?  @db.VarChar(100)
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  Identity    Identity[]
  Visa        Visa[]
  CallingCode CallingCode?
  City        City[]
  Currency    Currency?
  Port        Port[]
  CityRawData CityRawData[]
  PortRawData PortRawData[]
  ShipRawData ShipRawData[]
  Ship        Ship[]
  Profile     Profile[]
  Entity      Entity[]
  Timezone    Timezone[]

  @@index([name])
  @@schema("master")
}

model CallingCode {
  countryIso2 String   @id @unique @db.Char(2)
  code        String   @db.VarChar(10)
  createdAt   DateTime @default(now()) @db.Timestamptz()
  updatedAt   DateTime @updatedAt @db.Timestamptz()
  Country     Country  @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)

  @@index([code])
  @@schema("master")
}

model Timezone {
  timezone    String   @db.VarChar(500)
  countryIso2 String   @db.VarChar(2)
  utcOffset   Int
  dstOffset   Int
  createdAt   DateTime @default(now()) @db.Timestamptz()
  updatedAt   DateTime @updatedAt @db.Timestamptz()

  Country     Country       @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Port        Port[]
  PortRawData PortRawData[]

  @@id([timezone, countryIso2])
  @@index([timezone])
  @@schema("master")
}

model Currency {
  countryIso2 String   @id @unique @db.Char(2)
  code        String   @db.VarChar(5)
  name        String   @db.VarChar(50)
  symbol      String   @db.VarChar(50)
  numeric     Int
  decimal     Int
  createdAt   DateTime @default(now()) @db.Timestamptz()
  updatedAt   DateTime @updatedAt @db.Timestamptz()
  Country     Country  @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)

  @@index([code, name])
  @@schema("master")
}

//#endregion master

//#region network
enum RequestStatusE {
  PENDING
  ACCEPTED
  REJECTED
  REVOKED
  DISCONNECTED
  BLOCKED

  @@schema("network")
}

model Request {
  id                String         @id @default(uuid()) @db.Uuid
  senderProfileId   String         @db.Uuid
  receiverProfileId String         @db.Uuid
  count             Int            @default(1)
  status            RequestStatusE @default(PENDING)

  requestSentAt   DateTime @default(now()) @db.Timestamp()
  createdAt       DateTime @default(now()) @db.Timestamp()
  updatedAt       DateTime @updatedAt @db.Timestamp()
  ReceiverProfile Profile  @relation("ReceiverProfile", fields: [receiverProfileId], references: [id])
  SenderProfile   Profile  @relation("SenderProfile", fields: [senderProfileId], references: [id])

  @@unique([senderProfileId, receiverProfileId])
  @@schema("network")
}

model Connection {
  cursorId    BigInt   @default(autoincrement()) @db.BigInt()
  profileId   String   @db.Uuid
  connectedId String   @db.Uuid
  createdAt   DateTime @default(now()) @db.Timestamp()
  updatedAt   DateTime @updatedAt @db.Timestamp()
  ConnectedId Profile  @relation("ConnectedProfiles", fields: [connectedId], references: [id])
  Profile     Profile  @relation("ProfileConnections", fields: [profileId], references: [id])

  @@unique([profileId, connectedId])
  @@index([profileId, connectedId])
  @@schema("network")
}

model BlockedProfile {
  blockerId String   @db.Uuid
  blockedId String   @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()
  Blocked   Profile  @relation("BlockedProfile", fields: [blockedId], references: [id])
  Blocker   Profile  @relation("BlockerProfile", fields: [blockerId], references: [id])

  @@unique([blockerId, blockedId])
  @@schema("network")
}

model Follow {
  cursorId          BigInt   @default(autoincrement()) @db.BigInt()
  followerProfileId String   @db.Uuid
  followeeProfileId String   @db.Uuid
  createdAt         DateTime @default(now()) @db.Timestamp()
  updatedAt         DateTime @updatedAt @db.Timestamp()
  FollowerProfile   Profile  @relation("FollowerProfile", fields: [followerProfileId], references: [id])
  FolloweeProfile   Profile  @relation("FolloweeProfile", fields: [followeeProfileId], references: [id])

  @@unique([followerProfileId, followeeProfileId])
  @@index([cursorId])
  @@schema("network")
}

//#endregion network
//#region port
enum PortStatusE {
  PENDING
  REJECTED
  APPROVED
  SAVED

  @@schema("port")
}

enum ScrapBookPostStatusE {
  ACTIVE
  DELETED
  FLAGGED

  @@schema("port")
}

enum ScrapBookReactionTypeE {
  LIKE
  SUPPORT
  CELEBRATE
  FUNNY

  @@schema("port")
}

model Port {
  unLocode    String  @id @db.VarChar(5)
  name        String  @db.VarChar(255)
  imageUrl    String? @db.Text
  cityId      String  @db.Uuid
  countryIso2 String  @db.Char(2)
  timezone    String? @db.VarChar(500)

  latitude      Decimal? @db.Decimal(10, 7)
  longitude     Decimal? @db.Decimal(10, 7)
  noOfTerminals Int?
  noOfBerths    Int?
  maxDraught    Decimal? @db.Decimal(10, 2)
  maxDeadweight Decimal? @db.Decimal(8, 2)
  maxLength     Decimal? @db.Decimal(7, 2)
  maxAirDraught Decimal? @db.Decimal(10, 2)

  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  City    City    @relation(fields: [cityId], references: [id], onDelete: Cascade)
  Country Country @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)

  Timezone Timezone? @relation(fields: [timezone, countryIso2], references: [timezone, countryIso2], onDelete: Cascade)

  PortContribution      PortContribution[]
  PortImageContribution PortImageContribution[]
  PortVisitor           PortVisitor[]
  ScrapBookPost         ScrapBookPost[]
  CityRawData           CityRawData[]           @relation("CityRawDataToPort")

  @@index([unLocode])
  @@index([name])
  @@schema("port")
}

model PortImageContribution {
  id                  String       @id @default(uuid()) @db.Uuid
  portUnLocode        String?      @db.VarChar(5)
  portRawDataUnLocode String?      @db.VarChar(5)
  imageUrl            String       @db.Text
  dataStatus          PortStatusE  @default(PENDING)
  profileId           String       @db.Uuid
  createdAt           DateTime     @default(now()) @db.Timestamptz()
  updatedAt           DateTime     @updatedAt @db.Timestamptz()
  PortRawData         PortRawData? @relation(fields: [portRawDataUnLocode], references: [unLocode])
  Port                Port?        @relation(fields: [portUnLocode], references: [unLocode])
  Profile             Profile      @relation(fields: [profileId], references: [id])

  @@schema("port")
}

model PortContribution {
  id                  String       @id @default(uuid()) @db.Uuid
  portUnLocode        String?      @db.VarChar(5)
  portRawDataUnLocode String?      @db.VarChar(5)
  profileId           String       @db.Uuid
  label               String       @db.VarChar(100)
  value               String       @db.VarChar(100)
  dataStatus          PortStatusE  @default(PENDING)
  createdAt           DateTime     @default(now()) @db.Timestamptz()
  updatedAt           DateTime     @updatedAt @db.Timestamptz()
  PortRawData         PortRawData? @relation(fields: [portRawDataUnLocode], references: [unLocode])
  Port                Port?        @relation(fields: [portUnLocode], references: [unLocode])
  Profile             Profile      @relation(fields: [profileId], references: [id])

  @@unique([profileId, portUnLocode, portRawDataUnLocode, label])
  @@schema("port")
}

model ScrapBookPost {
  id                String               @id @default(uuid()) @db.Uuid
  text              String               @db.VarChar(1000)
  textPreview       String               @db.VarChar(120)
  profileId         String               @db.Uuid
  status            ScrapBookPostStatusE @default(ACTIVE)
  reactionCount     Int                  @default(0)
  commentCount      Int                  @default(0)
  portUnLocode      String               @db.VarChar(5)
  createdAt         DateTime             @default(now()) @db.Timestamptz()
  updatedAt         DateTime             @updatedAt @db.Timestamptz()
  ScrapBookComment  ScrapBookComment[]
  Port              Port                 @relation(fields: [portUnLocode], references: [unLocode])
  Profile           Profile              @relation(fields: [profileId], references: [id])
  ScrapBookReaction ScrapBookReaction[]

  @@schema("port")
}

model ScrapBookReaction {
  profileId       String                 @db.Uuid
  scrapBookPostId String                 @db.Uuid
  reactionType    ScrapBookReactionTypeE @default(LIKE)
  createdAt       DateTime               @default(now()) @db.Timestamp()
  updatedAt       DateTime               @updatedAt @db.Timestamptz()
  Profile         Profile                @relation(fields: [profileId], references: [id], onDelete: Cascade)
  ScrapBookPost   ScrapBookPost          @relation(fields: [scrapBookPostId], references: [id], onDelete: Cascade)

  @@id([profileId, scrapBookPostId])
  @@schema("port")
}

model ScrapBookComment {
  id                      String                    @id @default(uuid()) @db.Uuid
  text                    String                    @db.VarChar(300)
  cursorId                BigInt                    @default(autoincrement()) @db.BigInt()
  scrapBookPostId         String                    @db.Uuid
  profileId               String                    @db.Uuid
  status                  PostStatusE               @default(ACTIVE)
  parentCommentId         String?                   @db.Uuid
  repliesCount            Int?
  createdAt               DateTime                  @default(now()) @db.Timestamptz()
  updatedAt               DateTime                  @updatedAt @db.Timestamptz()
  Parent                  ScrapBookComment?         @relation("Replies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  Replies                 ScrapBookComment[]        @relation("Replies")
  Profile                 Profile                   @relation(fields: [profileId], references: [id])
  ScrapBookPost           ScrapBookPost             @relation(fields: [scrapBookPostId], references: [id], onDelete: Cascade)
  ScrapBookCommentMention ScrapBookCommentMention[]

  @@schema("port")
}

model ScrapBookCommentMention {
  id                 String           @id @default(uuid()) @db.Uuid
  commentId          String           @db.Uuid
  mentionedProfileId String           @db.Uuid
  createdAt          DateTime         @default(now()) @db.Timestamptz()
  updatedAt          DateTime         @updatedAt @db.Timestamptz()
  ScrapBookComment   ScrapBookComment @relation(fields: [commentId], references: [id])
  Profile            Profile          @relation(fields: [mentionedProfileId], references: [id])

  @@schema("port")
}

model PortVisitor {
  id                  String       @id @default(uuid()) @db.Uuid
  profileId           String       @db.Uuid
  portUnLocode        String?      @db.VarChar(5)
  portRawDataUnLocode String?      @db.VarChar(5)
  createdAt           DateTime     @default(now()) @db.Timestamptz()
  updatedAt           DateTime     @updatedAt @db.Timestamptz()
  PortRawData         PortRawData? @relation(fields: [portRawDataUnLocode], references: [unLocode])
  Port                Port?        @relation(fields: [portUnLocode], references: [unLocode])
  Profile             Profile      @relation(fields: [profileId], references: [id])

  @@schema("port")
}

//#endregion port

//#region rawData

enum StatusRawData {
  PENDING
  REJECTED
  APPROVED
  SAVED

  @@schema("rawData")
}

enum ServiceRawDataStatusE {
  ACTIVE
  DETAINED
  SCRAPPED

  @@schema("rawData")
}

enum ShipCapacityUnitTypeRawDataE {
  DEADWEIGHT_TONNAGE
  NOS
  LITRE
  LITRES_PER_HOUR
  CUBIC_METRE
  CUBIC_METRE_PER_HOUR
  TONNES
  TONNES_PER_HOUR
  TWENTY_FOOT_EQUIVALENT_UNIT

  @@schema("rawData")
}

enum EntityTypeRawDataE {
  COMPANY
  EDUCATION
  GO
  NGO
  IGO
  SMO
  OTHER

  @@schema("rawData")
}

enum SkillCategoryRawDataE {
  MARITIME
  OTHER

  @@schema("rawData")
}

enum CertificateCourseRawDataTypeE {
  STATUTORY
  VALUE_ADDED

  @@schema("rawData")
}

model DepartmentRawData {
  id             String           @id @default(uuid()) @db.Uuid
  name           String           @db.VarChar(100)
  dataStatus     StatusRawData    @default(PENDING)
  createdAt      DateTime         @default(now()) @db.Timestamp()
  updatedAt      DateTime         @updatedAt @db.Timestamp()
  ExperienceShip ExperienceShip[]
  Question       Question[]

  @@schema("rawData")
}

model DesignationRawData {
  id                    String                  @id @default(uuid()) @db.Uuid
  name                  String                  @db.VarChar(100)
  dataStatus            StatusRawData           @default(PENDING)
  createdAt             DateTime                @default(now()) @db.Timestamp()
  updatedAt             DateTime                @updatedAt @db.Timestamp()
  ExperienceDesignation ExperienceDesignation[]
  Profile               Profile[]

  @@schema("rawData")
}

model ShipRawData {
  imo                     String                  @id @db.VarChar(7)
  name                    String?                 @db.VarChar(100)
  flagCountryIso2         String?                 @db.Char(2)
  callSign                String?                 @db.VarChar(7)
  mmsi                    Int?                    @db.Integer
  length                  Int?                    @db.Integer
  beam                    Int?                    @db.Integer
  yearBuilt               Int?                    @db.Integer
  gt                      Int?                    @db.Integer
  dwt                     Int?                    @db.Integer
  mainVesselTypeId        String?                 @db.Uuid
  mainVesselTypeRawDataId String?                 @db.Uuid
  subVesselTypeId         String?                 @db.Uuid
  subVesselTypeRawDataId  String?                 @db.Uuid
  dataStatus              StatusRawData           @default(PENDING)
  createdAt               DateTime                @default(now()) @db.Timestamptz()
  updatedAt               DateTime                @updatedAt @db.Timestamptz()
  ExperienceShip          ExperienceShip[]
  Country                 Country?                @relation(fields: [flagCountryIso2], references: [iso2])
  MainVesselType          MainVesselType?         @relation(fields: [mainVesselTypeId], references: [id])
  MainVesselTypeRawData   MainVesselTypeRawData?  @relation(fields: [mainVesselTypeRawDataId], references: [id])
  SubVesselType           SubVesselType?          @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData    SubVesselTypeRawData?   @relation(fields: [subVesselTypeRawDataId], references: [id])
  ShipContribution        ShipContribution[]
  ShipImageContribution   ShipImageContribution[]
  Question                Question[]

  @@schema("rawData")
}

model CityRawData {
  id          String        @id @default(uuid()) @db.Uuid
  geoNameId   String?       @db.VarChar(15)
  name        String        @db.VarChar(50)
  countryIso2 String        @db.Char(2)
  dataStatus  StatusRawData @default(PENDING)
  createdAt   DateTime      @default(now()) @db.Timestamptz()
  updatedAt   DateTime      @updatedAt @db.Timestamptz()
  Country     Country       @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  PortRawData PortRawData[]
  Port        Port[]        @relation("CityRawDataToPort")

  @@index([name])
  @@schema("rawData")
}

model PortRawData {
  unLocode              String                  @id @unique @db.VarChar(5)
  name                  String?                 @db.VarChar(100)
  cityId                String?                 @db.Uuid
  cityRawDataId         String?                 @db.Uuid
  countryIso2           String                  @db.Char(2)
  timezoneIso2          String?                 @db.Char(2)
  latitude              Decimal?                @db.Decimal(6, 6)
  longitude             Decimal?                @db.Decimal(7, 6)
  noOfTerminals         Int?
  noOfBerths            Int?
  maxDraught            Decimal?                @db.Decimal(10, 2)
  maxDeadweight         Decimal?                @db.Decimal(8, 2)
  maxLength             Decimal?                @db.Decimal(7, 2)
  maxAirDraught         Decimal?                @db.Decimal(10, 2)
  dataStatus            StatusRawData           @default(PENDING)
  createdAt             DateTime                @default(now()) @db.Timestamptz()
  updatedAt             DateTime                @updatedAt @db.Timestamptz()
  PortContribution      PortContribution[]
  PortImageContribution PortImageContribution[]
  PortVisitor           PortVisitor[]
  City                  City?                   @relation(fields: [cityId], references: [id], onDelete: Cascade)
  CityRawData           CityRawData?            @relation(fields: [cityRawDataId], references: [id], onDelete: Cascade)
  Country               Country                 @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Timezone              Timezone?               @relation(fields: [timezoneIso2, countryIso2], references: [timezone, countryIso2], onDelete: Cascade)

  @@schema("rawData")
}

model SubVesselTypeRawData {
  id               String   @id @default(uuid()) @db.Uuid
  mainVesselTypeId String?  @db.Uuid
  name             String   @db.VarChar(100)
  createdAt        DateTime @default(now()) @db.Timestamptz()
  updatedAt        DateTime @updatedAt @db.Timestamptz()

  mainVesselType MainVesselType?  @relation(fields: [mainVesselTypeId], references: [id])
  ExperienceShip ExperienceShip[]
  ShipRawData    ShipRawData[]
  Question       Question[]

  @@schema("rawData")
}

model EntityRawData {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(255)
  website                       String?                         @db.VarChar(255)
  type                          EntityTypeE
  dataStatus                    StatusRawData                   @default(PENDING)
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  Experience                    Experience[]
  ProfileCertificate            ProfileCertificate[]
  ProfileEducation              ProfileEducation[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  Profile                       Profile[]

  @@schema("rawData")
}

model SkillRawData {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(100)
  category                      SkillCategoryRawDataE
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  ProfileSkill                  ProfileSkill[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  ProfileSkillExperienceShip    ProfileSkillExperienceShip[]

  @@schema("rawData")
}

model DegreeRawData {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(150)
  dataStatus               StatusRawData              @default(PENDING)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  ProfileEducation         ProfileEducation[]
  ProfileSkillEntityDegree ProfileSkillEntityDegree[]

  @@schema("rawData")
}

model TopicRawData {
  id            String          @default(uuid()) @db.Uuid
  name          String          @db.VarChar(100)
  count         Int             @default(0)
  createdAt     DateTime        @default(now()) @db.Timestamptz()
  updatedAt     DateTime        @updatedAt @db.Timestamptz()
  QuestionTopic QuestionTopic[]

  @@id([id])
  @@schema("rawData")
}

model CertificateCourseRawData {
  id                 String                 @id @default(uuid()) @db.Uuid
  name               String                 @db.VarChar(255)
  dataStatus         StatusRawData          @default(PENDING)
  type               CertificateCourseTypeE
  createdAt          DateTime               @default(now()) @db.Timestamptz()
  updatedAt          DateTime               @updatedAt @db.Timestamptz()
  ProfileCertificate ProfileCertificate[]

  @@schema("rawData")
}

model FuelTypeRawData {
  id                 String               @id @default(uuid()) @db.Uuid
  name               String               @db.VarChar(100)
  dataStatus         StatusRawData        @default(PENDING)
  createdAt          DateTime             @default(now()) @db.Timestamptz()
  updatedAt          DateTime             @updatedAt @db.Timestamptz()
  ExperienceFuelType ExperienceFuelType[]

  @@schema("rawData")
}

model EquipmentCategoryRawData {
  id                          String                        @id @default(uuid()) @db.Uuid
  name                        String                        @db.VarChar(100)
  hasFuelType                 Boolean                       @default(false)
  dataStatus                  StatusRawData                 @default(PENDING)
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ExperienceEquipmentCategory ExperienceEquipmentCategory[]
  Question                    Question[]

  @@schema("rawData")
}

model EquipmentManufacturerRawData {
  id         String        @id @default(uuid()) @db.Uuid
  name       String        @db.VarChar(100)
  dataStatus StatusRawData @default(PENDING)
  createdAt  DateTime      @default(now()) @db.Timestamptz()
  updatedAt  DateTime      @updatedAt @db.Timestamptz()
  Question   Question[]

  @@schema("rawData")
}

model EquipmentModelRawData {
  id         String        @id @default(uuid()) @db.Uuid
  name       String        @db.VarChar(100)
  dataStatus StatusRawData @default(PENDING)
  createdAt  DateTime      @default(now()) @db.Timestamptz()
  updatedAt  DateTime      @updatedAt @db.Timestamptz()
  Question   Question[]

  @@schema("rawData")
}

model MainVesselTypeRawData {
  id          String        @id @default(uuid()) @db.Uuid
  name        String        @db.VarChar(100)
  createdAt   DateTime      @default(now()) @db.Timestamptz()
  updatedAt   DateTime      @updatedAt @db.Timestamptz()
  ShipRawData ShipRawData[]
  Question    Question[]

  @@schema("rawData")
}

//#endregion rawData

//#region score
enum ScoreTypeE {
  CONTRIBUTION
  QNA_ANSWER
  TROUBLESHOOT_ANSWER

  @@schema("score")
}

model Reward {
  id             String           @default(uuid()) @db.Uuid
  type           ScoreTypeE
  score          Int
  createdAt      DateTime         @default(now()) @db.Timestamptz()
  updatedAt      DateTime         @updatedAt @db.Timestamptz()
  RewardAssigned RewardAssigned[]

  @@id([id])
  @@schema("score")
}

model RewardAssigned {
  id        String   @default(uuid()) @db.Uuid
  score     Int
  rewardId  String   @db.Uuid
  profileId String   @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  Reward  Reward  @relation(fields: [rewardId], references: [id])
  Profile Profile @relation(fields: [profileId], references: [id])

  @@id([id])
  @@schema("score")
}

model RewardProfile {
  profileId               String   @db.Uuid
  contributionScore       Int      @default(0)
  qnaAnswerScore          Int      @default(0)
  troubleshootAnswerScore Int      @default(0)
  totalScore              Int      @default(0)
  createdAt               DateTime @default(now()) @db.Timestamptz()
  updatedAt               DateTime @updatedAt @db.Timestamptz()

  Profile Profile @relation(fields: [profileId], references: [id])

  @@id([profileId])
  @@schema("score")
}

//#endregion score

//#region leaderboard
model WeeklyLeaderboard {
  id String @default(uuid()) @db.Uuid

  @@id([id])
  @@map("WeeklyLeaderboard")
  @@schema("score")
}

//#endregion leaderboard
//#region ship

enum ServiceStatusE {
  ACTIVE
  DETAINED
  SCRAPPED

  @@schema("ship")
}

enum ShipStatusE {
  PENDING
  REJECTED
  APPROVED
  SAVED

  @@schema("ship")
}

enum ShipContributionLabelE {
  mmsi
  callSign
  name
  flagCountryIso2
  generalVesselType
  otherVesselType
  status
  portOfRegistry
  yearBuilt

  @@schema("ship")
}

model Ship {
  imo                   String                  @id @db.VarChar(7)
  name                  String                  @db.VarChar(100)
  flagCountryIso2       String?                 @db.Char(2)
  callSign              String?                 @db.VarChar(7)
  mmsi                  Int?                    @db.Integer
  length                Int?                    @db.Integer
  beam                  Int?                    @db.Integer
  yearBuilt             Int?                    @db.Integer
  gt                    Int?                    @db.Integer
  dwt                   Int?                    @db.Integer
  imageUrl              String?                 @db.Text
  mainVesselTypeId      String                  @db.Uuid
  subVesselTypeId       String?                 @db.Uuid
  status                ServiceStatusE?
  createdAt             DateTime                @default(now()) @db.Timestamptz()
  updatedAt             DateTime                @updatedAt @db.Timestamptz()
  ExperienceShip        ExperienceShip[]
  Country               Country?                @relation(fields: [flagCountryIso2], references: [iso2])
  MainVesselType        MainVesselType?         @relation(fields: [mainVesselTypeId], references: [id])
  SubVesselType         SubVesselType?          @relation(fields: [subVesselTypeId], references: [id])
  ShipContribution      ShipContribution[]
  ShipImageContribution ShipImageContribution[]
  ShipName              ShipName[]
  Question              Question[]

  @@schema("ship")
}

model ShipName {
  id        String    @id @default(uuid()) @db.Uuid
  name      String    @db.VarChar(100)
  shipImo   String    @db.VarChar(7)
  fromDate  DateTime? @db.Date
  toDate    DateTime? @db.Date
  createdAt DateTime  @default(now()) @db.Timestamptz()
  updatedAt DateTime  @updatedAt @db.Timestamptz()
  Ship      Ship      @relation(fields: [shipImo], references: [imo])

  @@schema("ship")
}

model ShipImageContribution {
  id             String       @id @default(uuid()) @db.Uuid
  shipImo        String?      @db.VarChar(7)
  shipRawDataImo String?      @db.VarChar(7)
  imageUrl       String       @db.Text
  dataStatus     ShipStatusE  @default(PENDING)
  profileId      String?      @db.Uuid
  createdAt      DateTime     @default(now()) @db.Timestamptz()
  updatedAt      DateTime     @updatedAt @db.Timestamptz()
  Profile        Profile?     @relation(fields: [profileId], references: [id])
  Ship           Ship?        @relation(fields: [shipImo], references: [imo])
  ShipRawData    ShipRawData? @relation(fields: [shipRawDataImo], references: [imo])

  @@schema("ship")
}

model ShipContribution {
  id             String                 @id @default(uuid()) @db.Uuid
  shipImo        String?                @db.VarChar(7)
  shipRawDataImo String?                @db.VarChar(7)
  profileId      String                 @db.Uuid
  label          ShipContributionLabelE
  value          String                 @db.VarChar(100)
  dataStatus     ShipStatusE            @default(PENDING)
  createdAt      DateTime               @default(now()) @db.Timestamptz()
  updatedAt      DateTime               @updatedAt @db.Timestamptz()
  Profile        Profile                @relation(fields: [profileId], references: [id])
  Ship           Ship?                  @relation(fields: [shipImo], references: [imo])
  ShipRawData    ShipRawData?           @relation(fields: [shipRawDataImo], references: [imo])

  @@unique([profileId, shipImo, shipRawDataImo, label])
  @@schema("ship")
}

model FuelType {
  id                 String               @id @default(uuid()) @db.Uuid
  name               String               @db.VarChar(100)
  createdAt          DateTime             @default(now()) @db.Timestamptz()
  updatedAt          DateTime             @updatedAt @db.Timestamptz()
  ExperienceFuelType ExperienceFuelType[]

  @@schema("ship")
}

model EquipmentManufacturer {
  id        String     @id @default(uuid()) @db.Uuid
  name      String     @db.VarChar(100)
  createdAt DateTime   @default(now()) @db.Timestamptz()
  updatedAt DateTime   @updatedAt @db.Timestamptz()
  Question  Question[]

  @@schema("ship")
}

model EquipmentModel {
  id        String     @id @default(uuid()) @db.Uuid
  name      String     @db.VarChar(100)
  createdAt DateTime   @default(now()) @db.Timestamptz()
  updatedAt DateTime   @updatedAt @db.Timestamptz()
  Question  Question[]

  @@schema("ship")
}

model EquipmentCategory {
  id                          String                        @id @default(uuid()) @db.Uuid
  name                        String                        @db.VarChar(100)
  hasFuelType                 Boolean                       @default(false)
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ExperienceEquipmentCategory ExperienceEquipmentCategory[]
  Question                    Question[]

  @@schema("ship")
}

model MainVesselType {
  id        String   @id @default(uuid()) @db.Uuid
  name      String   @db.VarChar(100)
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  SubVesselType SubVesselType[]

  SubVesselTypeRawData SubVesselTypeRawData[]
  ShipRawData          ShipRawData[]
  Ship                 Ship[]
  Question             Question[]

  @@schema("ship")
}

model SubVesselType {
  id               String   @id @default(uuid()) @db.Uuid
  mainVesselTypeId String   @db.Uuid
  name             String   @db.VarChar(100)
  createdAt        DateTime @default(now()) @db.Timestamptz()
  updatedAt        DateTime @updatedAt @db.Timestamptz()

  MainVesselType MainVesselType   @relation(fields: [mainVesselTypeId], references: [id])
  ExperienceShip ExperienceShip[]
  Ship           Ship[]
  ShipRawData    ShipRawData[]
  Question       Question[]

  @@schema("ship")
}

enum ShipCapacityUnitTypeE {
  DEADWEIGHT_TONNAGE
  NOS
  LITRE
  LITRES_PER_HOUR
  CUBIC_METRE
  CUBIC_METRE_PER_HOUR
  TONNES
  TONNES_PER_HOUR
  TWENTY_FOOT_EQUIVALENT_UNIT

  @@schema("ship")
}

//#endregion ship
//#region user

enum GenderE {
  MALE
  FEMALE
  OTHER

  @@schema("user")
}

enum ProfileStatusE {
  ACTIVE
  INACTIVE
  SCHEDULED_FOR_DELETION
  BLOCKED
  DELETED

  @@schema("user")
}

model Profile {
  id                            String                          @default(uuid()) @db.Uuid
  email                         String                          @db.VarChar(255)
  password                      String?
  username                      String?                         @db.VarChar(25)
  googleSub                     String?                         @db.VarChar(255)
  appleSub                      String?                         @db.VarChar(255)
  name                          String?                         @db.VarChar(50)
  avatar                        String?                         @db.Text
  gender                        GenderE?
  status                        ProfileStatusE                  @default(ACTIVE)
  description                   String?                         @db.VarChar(255)
  countryIso2                   String?                         @db.Char(2)
  designationText               String?                         @db.VarChar(100)
  entityText                    String?                         @db.VarChar(255)
  designationAlternativeId      String?                         @db.Uuid
  designationRawDataId          String?                         @db.Uuid
  entityId                      String?                         @db.Uuid
  entityRawDataId               String?                         @db.Uuid
  followersCount                Int                             @default(0)
  followingsCount               Int                             @default(0)
  connectionsCount              Int                             @default(0)
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  Experience                    Experience[]
  ProfileCertificate            ProfileCertificate[]
  ProfileEducation              ProfileEducation[]
  Identity                      Identity[]
  Visa                          Visa[]
  Posts                         Post[]
  PostComments                  PostComment[]
  PostCommentReactions          PostCommentReaction[]
  PostMedia                     PostMedia[]
  PostReactions                 PostReaction[]
  BlockedByProfile              BlockedProfile[]                @relation("BlockedProfile")
  BlockedProfile                BlockedProfile[]                @relation("BlockerProfile")
  ConnectedId                   Connection[]                    @relation("ConnectedProfiles")
  Connections                   Connection[]                    @relation("ProfileConnections")
  ReceivedRequests              Request[]                       @relation("ReceiverProfile")
  SentRequests                  Request[]                       @relation("SenderProfile")
  Follows                       Follow[]                        @relation("FollowerProfile")
  Following                     Follow[]                        @relation("FolloweeProfile")
  PortContribution              PortContribution[]
  PortImageContribution         PortImageContribution[]
  PortVisitor                   PortVisitor[]
  ScrapBookComment              ScrapBookComment[]
  ScrapBookCommentMention       ScrapBookCommentMention[]
  ScrapBookPost                 ScrapBookPost[]
  ScrapBookReaction             ScrapBookReaction[]
  ShipContribution              ShipContribution[]
  ShipImageContribution         ShipImageContribution[]
  Country                       Country?                        @relation(fields: [countryIso2], references: [iso2])
  DesignationAlternative        DesignationAlternative?         @relation(fields: [designationAlternativeId], references: [id])
  DesignationRawData            DesignationRawData?             @relation(fields: [designationRawDataId], references: [id])
  Entity                        Entity?                         @relation(fields: [entityId], references: [id])
  EntityRawData                 EntityRawData?                  @relation(fields: [entityRawDataId], references: [id])
  ProfileStatus                 ProfileStatus?
  ProfileMeta                   ProfileMeta?
  ProfileSkill                  ProfileSkill[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ExperienceDesignation         ExperienceDesignation[]
  ExperienceShip                ExperienceShip[]
  CommunityMember               CommunityMember[]
  Community                     Community[]
  Question                      Question[]
  Answer                        Answer[]
  CommunityRequest              CommunityRequest[]
  QuestionComment               QuestionComment[]
  QuestionVote                  QuestionVote[]
  AnswerVote                    AnswerVote[]
  AnswerComment                 AnswerComment[]
  ValidatedBy                   Answer[]                        @relation("ValidatedBy")
  RewardAssigned                RewardAssigned[]
  RewardProfile                 RewardProfile[]

  @@id([id])
  @@schema("user")
}

model ProfileStatus {
  profileId               String    @db.Uuid
  isPasswordSaved         Boolean   @default(false)
  isEmailVerified         Boolean   @default(false)
  isMobileVerified        Boolean   @default(false)
  isPersonalDetailsSaved  Boolean   @default(false)
  isWorkDetailsSaved      Boolean   @default(false)
  isPrivacyPolicyAccepted Boolean   @default(false)
  deletionInitiatedAt     DateTime?
  createdAt               DateTime  @default(now()) @db.Timestamp()
  updatedAt               DateTime  @updatedAt @db.Timestamp()
  Profile                 Profile   @relation(fields: [profileId], references: [id])

  @@id(profileId)
  @@schema("user")
}

model ProfileMeta {
  profileId                       String    @db.Uuid
  receivedRequestCount            Int       @default(0)
  sentRequestCount                Int       @default(0)
  postCount                       Int       @default(0)
  educationCount                  Int       @default(0)
  statutoryCertCount              Int       @default(0)
  valueAddedCertCount             Int       @default(0)
  identityCount                   Int       @default(0)
  visaCount                       Int       @default(0)
  maritimeSkillsCount             Int       @default(0)
  otherSkillsCount                Int       @default(0)
  answerCount                     Int       @default(0)
  solutionCount                   Int       @default(0)
  normalQuestionScore             Int       @default(0)
  troubleshootQuestionScore       Int       @default(0)
  contributionScore               Int       @default(0)
  emailVerificationCount          Int       @default(0)
  emailVerificationFirstAttemptAt DateTime?
  passwordResetCount              Int       @default(0)
  passwordResetFirstAttemptAt     DateTime?

  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()
  Profile   Profile  @relation(fields: [profileId], references: [id])

  @@id(profileId)
  @@schema("user")
}

//#endregion user

enum PostStatusE {
  ACTIVE
  DELETED
  FLAGGED

  @@schema("feed")
}

enum ReactionTypeE {
  LIKE
  SUPPORT
  CELEBRATE
  FUNNY

  @@schema("feed")
}

enum EntityTypeE {
  COMPANY
  EDUCATION
  GO
  NGO
  IGO
  SMO
  OTHER

  @@schema("company")
}

enum SkillCategoryE {
  MARITIME
  OTHER

  @@schema("company")
}

enum CertificateCourseTypeE {
  STATUTORY
  VALUE_ADDED

  @@schema("company")
}

enum EquipmentCategoryPowerCapacityUnitE {
  KILO_WATT
  LITRE
  LITRES_PER_HOUR
  CUBIC_METRE
  CUBIC_METRE_PER_HOUR
  TONNES
  TONNES_PER_HOUR

  @@schema("career")
}

enum IdentityTypeE {
  PASSPORT
  CDC
  SID

  @@schema("document")
}
