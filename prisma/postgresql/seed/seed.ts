import * as fs from 'fs';
import { seedMeta } from './common';
import { prismaPG } from '../../../src/config/db';
import { BatchInput, SeedInput, UnsavedRecord } from './types';

export const processInBatches = async <T>({
  inputs,
  callbackFn,
  batchSize,
}: BatchInput<T>): Promise<unknown[]> => {
  const output: unknown[] = [];
  for (let index = 0; index < inputs.length; index += batchSize) {
    const batchInput = inputs.slice(index, index + batchSize);
    const batchResult = await Promise.all(
      batchInput.map((inputItem) => callbackFn(inputItem)),
    );
    output.push(...batchResult);
  }
  return output;
};

const unsavedRecords: UnsavedRecord[] = [];

const seedData = async ({
  table,
  record,
  objId,
  id,
  index,
  fileName,
}: SeedInput) => {
  try {
    const where: Record<string, unknown> = objId
    ? id.reduce((acc, field) => {
        const existingObj = acc[objId] as Record<string, unknown> | undefined;
        acc[objId] = {
          ...(existingObj ? existingObj : {}),
          [field]: record[field]
        };
        return acc;
      }, {} as Record<string, Record<string, unknown>>)
    : id.reduce((acc, field) => {
        acc[field] = record[field];
        return acc;
      }, {} as Record<string, unknown>);
    await (
      table as unknown as {
        upsert: (args: {
          where: Record<string, unknown>;
          create: typeof record;
          update: typeof record;
        }) => Promise<void>;
      }
    ).upsert({
      where,
      create: record,
      update: record,
    });
  } catch (error) {
    unsavedRecords.push({
      record,
      error: error instanceof Error ? error.message : String(error),
      index,
      fileName,
    });
  }
};

const main = async () => {
  console.time('Seed Time');

  for (const { table, fileName, objId, id } of seedMeta) {
    const filePath = `prisma/postgresql/data/${fileName}`;
    const fileContents = fs.readFileSync(filePath, 'utf-8');
    const records: Record<string, unknown>[] = JSON.parse(fileContents);

    await processInBatches<SeedInput>({
      inputs: records.map((record, index) => ({
        table,
        record,
        objId,
        id,
        index,
        fileName,
      })),
      callbackFn: seedData,
      batchSize: 7,
    });

  }

  if (unsavedRecords.length > 0) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `unsavedRecords-${timestamp}.json`;
    fs.writeFileSync(fileName, JSON.stringify(unsavedRecords, null, 2), 'utf-8');
  }

  console.timeEnd('Seed Time');
};

(async () => {
  try {
    await main();
  } catch (_error) {
    // process.exit(1);
  } finally {
    await prismaPG.$disconnect();
  }
})();
