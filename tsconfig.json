{"compilerOptions": {"module": "CommonJS", "target": "ES2022", "rootDir": "./src", "outDir": "./dist", "sourceMap": true, "esModuleInterop": true, "baseUrl": "./src", "paths": {"@classes/*": ["classes/*"], "@config/*": ["config/*"], "@consts/*": ["consts/*"], "@interfaces/*": ["interfaces/*"], "@middlewares/*": ["middlewares/*"], "@modules/*": ["modules/*"], "@routes/*": ["routes/*"], "@schemas/*": ["schemas/*"], "services/*": ["services/*"], "@utils/*": ["utils/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/utils/generator/**/*.ts", "src/consts/forum/request.ts", "src/modules/forum/answer/vote.ts"], "exclude": ["node_modules", "dist"]}