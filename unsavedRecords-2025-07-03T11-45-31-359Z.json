[{"record": {"id": "18725f2c-09a6-40ec-a8cf-1826ab164fcb", "platformId": "android", "versionNo": "0.0.3", "isActive": true, "createdAt": "2025-07-03T08:45:39.004Z", "updatedAt": "2025-07-03T08:45:39.004Z"}, "error": "\nInvalid `).upsert()` invocation in\n/app/prisma/postgresql/seed/seed.ts:54:7\n\n  51       update: typeof record;\n  52     }) => Promise<void>;\n  53   }\n→ 54 ).upsert(\nAn operation failed because it depends on one or more records that were required but not found. Record not found", "index": 0, "fileName": "appVersion.json"}, {"record": {"id": "cacedaca-a96d-4237-9477-7f782f9976e8", "platformId": "ios", "versionNo": "0.0.3", "isActive": true, "createdAt": "2025-07-03T08:45:39.004Z", "updatedAt": "2025-07-03T08:45:39.004Z"}, "error": "\nInvalid `).upsert()` invocation in\n/app/prisma/postgresql/seed/seed.ts:54:7\n\n  51       update: typeof record;\n  52     }) => Promise<void>;\n  53   }\n→ 54 ).upsert(\nAn operation failed because it depends on one or more records that were required but not found. Record not found", "index": 1, "fileName": "appVersion.json"}]